[tool.poetry]
name = "backend"
version = "0.1.0"
description = "Backend of the Minimum Varinace Portfolio Builder for CSE Stocks Using Modern Portfolio Theory"
authors = ["Tharinda Attanayake <<EMAIL>>"]
license = "Apache License 2.0"
readme = "README.md"
packages = [{include = "app"}]

[tool.poetry.dependencies]
python = ">=3.12"
fastapi = ">=0.115.11,<0.116.0"
uvicorn = "^0.28.0"
pydantic = "^2.6.1"
motor = "^3.7.0"
numpy = "^2.2.3"
pandas = "^2.2.3"
python-dotenv = "^1.0.1"
scikit-learn = "^1.6.1"
matplotlib = "^3.10.1"
plotly = "^6.0.1"
setuptools = "^77.0.3"
pymongo = "^4.11.3"
apscheduler = "^3.11.0"
httpx = "^0.28.1"
python-jose = {extras = ["cryptography"], version = "^3.4.0"}
svix = "^1.64.1"
clerk-backend-api = "^2.0.2"
pylint = "^3.3.6"
python-dateutil = "^2.8.2"
aiohttp = "^3.11.18"

[build-system]
requires = ["poetry-core>=2.0.0,<3.0.0"]
build-backend = "poetry.core.masonry.api"

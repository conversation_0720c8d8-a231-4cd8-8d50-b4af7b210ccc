"""
Configuration Module

This module contains all configuration settings for the application, including:
- Environment variables
- Default values for portfolio optimization
- Constants used throughout the application
"""

import os
from datetime import datetime, timedelta
from typing import Dict, Any
from pydantic import BaseModel, Field, field_validator
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

#
# Environment Settings
#

class Settings(BaseModel):
    """Application settings loaded from environment variables with validation."""

    # MongoDB settings
    MONGO_URI: str = Field(..., description="MongoDB connection string")
    DATABASE_NAME: str = Field(..., description="MongoDB database name")

    # CSE API settings
    CSE_API_HISTORICAL_DATA_URL: str = Field(..., description="CSE API URL for historical trades")
    CSE_API_DAILY_SUMMARY_DATA_URL: str = Field(..., description="CSE API URL for daily trade summary")
    CSE_API_TOKEN_URL: str = Field(..., description="CSE API URL for authentication")
    CSE_API_ASPI_DATA_URL: str = Field(..., description="CSE API URL for ASPI index data")
    CSE_API_SNPSL_DATA_URL: str = Field(..., description="CSE API URL for S&PSL20 index data")
    CSE_API_NEWS_URL: str = Field(..., description="CSE API URL for news")
    CSE_API_TOP_GAINERS_URL: str = Field(..., description="CSE API URL for top gainers")
    CSE_API_TOP_LOSERS_URL: str = Field(..., description="CSE API URL for top losers")
    CSE_API_MOST_ACTIVE_TRADES_URL: str = Field(..., description="CSE API URL for most active trades")
    CSE_API_USERNAME: str = Field(..., description="CSE API username")
    CSE_API_PASSWORD: str = Field(..., description="CSE API password")
    CLERK_SECRET_KEY: str = Field(..., description="Clerk secret key")
    CLERK_JWT_KEY: str = Field(..., description="Clerk JWT key")
    CLERK_API_URL: str = Field(..., description="Clerk API URL")
    CLERK_WEBHOOK_SECRET: str = Field(..., description="Clerk webhook secret")
    CLERK_WEBHOOK_SIGNING_SECRET: str = Field(..., description="Clerk webhook signing secret")
    @field_validator('MONGO_URI')
    @classmethod
    def validate_mongo_uri(cls, v):
        """Validate that the MongoDB URI is properly formatted."""
        if not v.startswith(('mongodb://', 'mongodb+srv://')):
            raise ValueError("MONGO_URI must start with 'mongodb://' or 'mongodb+srv://'")
        return v

def get_settings() -> Settings:
    """
    Get application settings from environment variables.

    Returns:
        Settings: Application settings

    Raises:
        ValueError: If required environment variables are missing
    """
    try:
        return Settings(
            MONGO_URI=os.getenv("MONGO_URI", ""),
            DATABASE_NAME=os.getenv("DATABASE_NAME", "MVP_DB"),
            CSE_API_HISTORICAL_DATA_URL=os.getenv("CSE_API_HISTORICAL_DATA_URL", "https://www.cse.lk/api/historicalTrades"),
            CSE_API_DAILY_SUMMARY_DATA_URL=os.getenv("CSE_API_DAILY_SUMMARY_DATA_URL", "https://www.cse.lk/api/tradeSummary"),
            CSE_API_TOKEN_URL=os.getenv("CSE_API_TOKEN_URL", "https://www.cse.lk/api/signInNew"),
            CSE_API_ASPI_DATA_URL=os.getenv("CSE_API_ASPI_DATA_URL", "https://www.cse.lk/api/aspiData"),
            CSE_API_SNPSL_DATA_URL=os.getenv("CSE_API_SNPSL_DATA_URL", "https://www.cse.lk/api/snpData"),
            CSE_API_NEWS_URL=os.getenv("CSE_API_NEWS_URL", "https://www.cse.lk/api/news/web"),
            CSE_API_TOP_GAINERS_URL=os.getenv("CSE_API_TOP_GAINERS_LOSERS_URL", "https://www.cse.lk/api/topGainers"),
            CSE_API_TOP_LOSERS_URL=os.getenv("CSE_API_TOP_LOSERS_URL", "https://www.cse.lk/api/topLooses"),
            CSE_API_MOST_ACTIVE_TRADES_URL=os.getenv("CSE_API_MOST_ACTIVE_TRADES_URL", "https://www.cse.lk/api/mostActiveTrades"),
            CSE_API_USERNAME=os.getenv("CSE_API_USERNAME", ""),
            CSE_API_PASSWORD=os.getenv("CSE_API_PASSWORD", ""),
            CLERK_SECRET_KEY=os.getenv("CLERK_SECRET_KEY", ""),
            CLERK_JWT_KEY=os.getenv("CLERK_JWT_KEY", ""),
            CLERK_API_URL=os.getenv("CLERK_API_URL", "https://api.clerk.dev/v1"),
            CLERK_WEBHOOK_SECRET=os.getenv("CLERK_WEBHOOK_SECRET", ""),
            CLERK_WEBHOOK_SIGNING_SECRET=os.getenv("CLERK_WEBHOOK_SIGNING_SECRET", ""),
            )
    except Exception as e:
        missing_vars = []
        for field in Settings.model_fields.keys():
            if os.getenv(field) is None:
                missing_vars.append(field)

        if missing_vars:
            raise ValueError(
                f"Missing required environment variables: {', '.join(missing_vars)}. "
                f"Please check your .env file or environment variables."
            ) from e
        raise

# Create settings instance
settings = get_settings()

#
# Portfolio Default Configuration
#

# Default date range (5 years)
DEFAULT_END_DATE = datetime.now().strftime("%Y-%m-%d")
DEFAULT_START_DATE = (datetime.now() - timedelta(days=365 * 5)).strftime("%Y-%m-%d")

# Default risk-free rate (8%)
DEFAULT_RISK_FREE_RATE = 0.08

# Default weight constraints
DEFAULT_MIN_WEIGHT = 0.0  # 0% minimum weight per stock (allowing natural exclusion)
DEFAULT_MAX_WEIGHT = 0.35  # 35% maximum weight per stock (prevents over-allocation)
DEFAULT_WEIGHT_BOUNDS = (DEFAULT_MIN_WEIGHT, DEFAULT_MAX_WEIGHT)

# Sector constraints have been removed from the application

# Statistical refinement defaults - not exposed to users
# Winsorization is always applied at 2.5% level internally

# Default portfolio types
PORTFOLIO_TYPE_MIN_VARIANCE = "min_variance"
PORTFOLIO_TYPE_MAX_SHARPE = "max_sharpe"
PORTFOLIO_TYPE_CUSTOM = "custom"

def get_default_constraints() -> Dict[str, Any]:
    """
    Get the default constraints for portfolio optimization.

    Returns:
        Dictionary containing default constraints
    """
    return {
        "start_date": DEFAULT_START_DATE,  # Keep for backward compatibility
        "end_date": DEFAULT_END_DATE,      # Keep for backward compatibility
        "from_date": DEFAULT_START_DATE,   # New naming convention
        "to_date": DEFAULT_END_DATE,       # New naming convention
        "risk_free_rate": DEFAULT_RISK_FREE_RATE,
        "weight_bounds": DEFAULT_WEIGHT_BOUNDS
    }

def get_portfolio_type_description(portfolio_type: str) -> str:
    """
    Get a description for a portfolio type.

    Args:
        portfolio_type: The type of portfolio

    Returns:
        A description of the portfolio type
    """
    descriptions = {
        PORTFOLIO_TYPE_MIN_VARIANCE: "Minimum Variance Portfolio (lowest possible volatility)",
        PORTFOLIO_TYPE_MAX_SHARPE: "Maximum Sharpe Ratio Portfolio (optimal risk-adjusted return)",
        PORTFOLIO_TYPE_CUSTOM: "Custom Portfolio (user-defined constraints)"
    }

    return descriptions.get(portfolio_type, "Unknown portfolio type")

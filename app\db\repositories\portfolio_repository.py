"""
Portfolio Repository

This module provides database operations for portfolios.
"""

import logging
import uuid
from datetime import datetime
from typing import Dict, List, Optional, Any, cast, TypeVar, Generic
from bson.decimal128 import Decimal128
from motor.motor_asyncio import AsyncIOMotorDatabase, AsyncIOMotorCursor
from pymongo.results import InsertOneResult, DeleteResult, UpdateResult

from app.db.session import get_database  # Import the database connection function
from app.utils.db_utils import safe_mongodb_operation

logger = logging.getLogger(__name__)

T = TypeVar('T')

class PortfolioRepository:
    """Repository for portfolio operations."""

    @staticmethod
    async def create_portfolio(portfolio_data: Dict[str, Any]) -> Optional[str]:
        """
        Create a new portfolio with embedded holdings.

        Args:
            portfolio_data: Portfolio data to insert

        Returns:
            Portfolio ID if successful, None otherwise
        """
        try:
            # Get the database for the current event loop
            db: AsyncIOMotorDatabase = await get_database()
            portfolio_collection = db["portfolios"]

            # Generate a unique portfolio ID
            portfolio_id = f"PF-{datetime.now().strftime('%Y%m')}-{str(uuid.uuid4())[:8]}"

            # Add portfolio ID and dates
            current_time = datetime.now()
            portfolio_data["portfolioId"] = portfolio_id
            portfolio_data["creationDate"] = current_time
            portfolio_data["lastRebalancedDate"] = current_time
            portfolio_data["lastUpdatedDate"] = current_time
            portfolio_data["isActive"] = True

            # Set total unrealized gain/loss to 0 (placeholder)
            portfolio_data["totalUnrealizedGainLoss"] = Decimal128('0')

            # Ensure holdings have unrealized gain/loss set to 0
            if "holdings" in portfolio_data:
                for holding in portfolio_data["holdings"]:
                    holding["unrealizedGainLoss"] = Decimal128('0')

            # Insert the portfolio
            result = await safe_mongodb_operation(
                lambda: portfolio_collection.insert_one(portfolio_data),
                fallback_value=None
            )

            if result and hasattr(result, 'inserted_id') and result.inserted_id:
                logger.info(f"Created portfolio with ID: {portfolio_id}")
                return portfolio_id

            logger.error("Failed to create portfolio")
            return None
        except Exception as e:
            logger.error(f"Error creating portfolio: {str(e)}")
            return None

    @staticmethod
    async def get_portfolio(portfolio_id: str) -> Optional[Dict[str, Any]]:
        """
        Get a portfolio by ID.

        Args:
            portfolio_id: Portfolio ID

        Returns:
            Portfolio data if found, None otherwise
        """
        try:
            # Get the database for the current event loop
            db: AsyncIOMotorDatabase = await get_database()
            portfolio_collection = db["portfolios"]

            portfolio = await safe_mongodb_operation(
                lambda: portfolio_collection.find_one({"portfolioId": portfolio_id}),
                fallback_value=None
            )

            return cast(Optional[Dict[str, Any]], portfolio)
        except Exception as e:
            logger.error(f"Error getting portfolio {portfolio_id}: {str(e)}")
            return None

    @staticmethod
    async def get_all_portfolios() -> List[Dict[str, Any]]:
        """
        Get all portfolios.

        Returns:
            List of portfolios
        """
        try:
            # Get the database for the current event loop
            db: AsyncIOMotorDatabase = await get_database()
            portfolio_collection = db["portfolios"]

            # No filter needed since we use hard delete
            query = {}

            portfolios = await safe_mongodb_operation(
                lambda: portfolio_collection.find(query).to_list(length=None),
                fallback_value=[]
            )

            return cast(List[Dict[str, Any]], portfolios)
        except Exception as e:
            logger.error(f"Error getting all portfolios: {str(e)}")
            return []



    @staticmethod
    async def delete_portfolio(portfolio_id: str) -> bool:
        """
        Delete a portfolio by ID.

        Args:
            portfolio_id: Portfolio ID

        Returns:
            True if deletion was successful

        Raises:
            ValueError: If the portfolio with the given ID was not found
            RuntimeError: If the database operation failed
        """
        try:
            # Get the database for the current event loop
            db: AsyncIOMotorDatabase = await get_database()
            portfolio_collection = db["portfolios"]

            # Always perform hard delete - completely remove the portfolio
            result = await safe_mongodb_operation(
                lambda: portfolio_collection.delete_one({"portfolioId": portfolio_id}),
                fallback_value=None
            )

            if result and hasattr(result, 'deleted_count'):
                if result.deleted_count > 0:
                    logger.info(f"Deleted portfolio {portfolio_id}")
                    return True
                elif result.deleted_count == 0:
                    logger.warning(f"Portfolio {portfolio_id} not found for deletion")
                    raise ValueError(f"Portfolio {portfolio_id} not found")

            # result is None or doesn't have deleted_count – operation failed
            logger.error(f"Database operation failed when deleting portfolio {portfolio_id}")
            raise RuntimeError("Database deletion failed")
        except (ValueError, RuntimeError):
            # Re-raise these specific exceptions
            raise
        except Exception as e:
            logger.error(f"Error deleting portfolio {portfolio_id}: {str(e)}")
            raise RuntimeError(f"Failed to delete portfolio: {str(e)}")

    @staticmethod
    async def update_portfolio_values(
        portfolio_id: str,
        holdings: List[Dict[str, Any]],
        total_market_value: Decimal128,
        total_unrealized_gain_loss: Decimal128,
        last_updated_date: datetime
    ) -> bool:
        """
        Update portfolio with new market values and unrealized gains/losses.

        Args:
            portfolio_id: Portfolio ID
            holdings: Updated holdings list
            total_market_value: New total market value
            total_unrealized_gain_loss: New total unrealized gain/loss
            last_updated_date: Date of the update

        Returns:
            True if update was successful
        """
        try:
            # Get the database for the current event loop
            db: AsyncIOMotorDatabase = await get_database()
            portfolio_collection = db["portfolios"]

            # Update the portfolio
            result = await safe_mongodb_operation(
                lambda: portfolio_collection.update_one(
                    {"portfolioId": portfolio_id},
                    {
                        "$set": {
                            "holdings": holdings,
                            "totalMarketValue": total_market_value,
                            "totalUnrealizedGainLoss": total_unrealized_gain_loss,
                            "lastUpdatedDate": last_updated_date
                        }
                    }
                ),
                fallback_value=None
            )

            if result and hasattr(result, 'matched_count') and result.matched_count > 0:
                logger.info(f"Updated portfolio {portfolio_id}")
                return True

            logger.error(f"Failed to update portfolio {portfolio_id}")
            return False
        except Exception as e:
            logger.error(f"Error updating portfolio {portfolio_id}: {str(e)}")
            return False

    @staticmethod
    async def update_portfolio_after_rebalance(
        portfolio_id: str,
        new_holdings: List[Dict[str, Any]],
        total_market_value: Decimal128,
        annualized_return: Decimal128,
        annualized_volatility: Decimal128,
        last_rebalanced_date: datetime
    ) -> bool:
        """
        Update portfolio after rebalancing with new holdings and performance metrics.

        Args:
            portfolio_id: Portfolio ID
            new_holdings: New holdings list after rebalancing
            total_market_value: New total market value
            annualized_return: New annualized return
            annualized_volatility: New annualized volatility
            last_rebalanced_date: Date of the rebalance

        Returns:
            True if update was successful, False otherwise
        """
        try:
            # Get the database for the current event loop
            db: AsyncIOMotorDatabase = await get_database()
            portfolio_collection = db["portfolios"]

            result = await safe_mongodb_operation(
                lambda: portfolio_collection.update_one(
                    {"portfolioId": portfolio_id},
                    {
                        "$set": {
                            "holdings": new_holdings,
                            "totalMarketValue": total_market_value,
                            "totalUnrealizedGainLoss": Decimal128('0'),  # Reset to zero after rebalance
                            "annualizedReturn": annualized_return,
                            "annualizedVolatility": annualized_volatility,
                            "lastRebalancedDate": last_rebalanced_date,
                            "lastUpdatedDate": last_rebalanced_date
                        }
                    }
                )
            )

            if result and result.matched_count > 0:
                if result.modified_count > 0:
                    logger.info(f"Rebalanced portfolio {portfolio_id} with new holdings and performance metrics")
                else:
                    logger.info(f"Portfolio {portfolio_id} rebalance had no effect (no changes needed)")
                return True

            logger.warning(f"Portfolio {portfolio_id} not found for rebalancing")
            return False
        except Exception as e:
            logger.error(f"Error rebalancing portfolio {portfolio_id}: {str(e)}")
            return False

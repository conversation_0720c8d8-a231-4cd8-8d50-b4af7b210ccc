'''
This module contains the controller for the news data that comes from the CSE API.
'''

from datetime import datetime
from fastapi import APIRouter, HTTPException, Query
from app.services.news_services import get_news
from app.models.news_models import MonthlyNews

router = APIRouter(prefix="/api/news", tags=["News"])

@router.get("/", response_model=MonthlyNews)
async def get_news_endpoint(
    year: int = Query(default=datetime.now().year, description="Year to filter news by"),
    month: str = Query(default=datetime.now().strftime("%B"), description="Month to filter news by")
):
    '''
    Get the news for a specific year and month.
    '''

    try:
        query_params = {
            "year": year,
            "month": month
        }
        return await get_news(query_params)

    except HTTPException as e:
        raise e
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e)) from e

"""
Trade Controller

This module provides endpoints for fetching and storing daily trade summary data.
"""

import logging
from datetime import datetime
from fastapi import APIRouter, HTTPException, Query
from app.services.cse_trade_summary_service import fetch_and_store_trade_summary, get_top_gainers_losers, get_trades, get_most_active_trades
from app.models.trade_models import TopGainersLosersResponse, TradesResponse, MostActiveTrade
from app.services.scheduler_service import (
    scheduler, scheduled_fetch_daily_trades, fetch_daily_trades_scheduler,
    manual_fetch_index_data
)

router = APIRouter(prefix="/api/trades", tags=["Trades"])
logger = logging.getLogger(__name__)

# -------------GET REQUESTS-------------
@router.get("/fetch-daily-trades")
async def fetch_daily_trades(force: bool = False):
    """Fetch and store daily trade summary data.

    Args:
        force: If True, fetch data even on weekends or if data already exists
    """
    logger.info("Manual trigger: Starting daily trade data fetch (force=%s)", force)
    try:
        result = await fetch_and_store_trade_summary(force_fetch=force)

        # Log appropriate message based on result
        if result.get("error"):
            logger.error("Manual trigger: %s", result.get('message'))
        elif result.get("trade_date"):
            if result.get("records_inserted", 0) > 0:
                logger.info("Manual trigger: Inserted %d records for trade date %s",
                            result.get('records_inserted'),
                            result.get('trade_date'))
            else:
                logger.info("Manual trigger: %s for trade date %s",
                            result.get('message'),
                            result.get('trade_date'))
        else:
            logger.info("Manual trigger: %s", result.get('message'))

        logger.info("Manual trigger: Completed daily trade data fetch")
        return result
    except Exception as e:
        error_msg = f"Error fetching daily trades: {str(e)}"
        logger.error(error_msg)
        raise HTTPException(status_code=500, detail=error_msg) from e

@router.get("/test-scheduler")
async def test_scheduler():
    """Test the scheduler by manually running the scheduled job.

    This endpoint is useful for debugging scheduler issues.
    """
    logger.info("Manual test: Running scheduled job directly")
    try:
        await scheduled_fetch_daily_trades()
        return {"message": "Scheduled job executed successfully. Check logs for details."}
    except Exception as e:
        error_msg = f"Error executing scheduled job: {str(e)}"
        logger.error(error_msg)
        raise HTTPException(status_code=500, detail=error_msg) from e

@router.get("/scheduler-status")
async def get_scheduler_status():
    """Get the current status of the scheduler and its jobs.

    Returns:
        dict: Information about the scheduler status and jobs
    """
    try:
        jobs = []
        for job in scheduler.get_jobs():
            jobs.append({
                "id": job.id,
                "name": job.name,
                "next_run_time": str(job.next_run_time) if job.next_run_time else None,
                "trigger": str(job.trigger)
            })

        return {
            "scheduler_running": scheduler.running,
            "jobs": jobs
        }
    except Exception as e:
        error_msg = f"Error getting scheduler status: {str(e)}"
        logger.error(error_msg)
        raise HTTPException(status_code=500, detail=error_msg) from e

@router.post("/restart-scheduler")
async def restart_scheduler():
    """Restart the scheduler service.

    This endpoint can be used to restart the scheduler if it has stopped or needs to be refreshed.
    """
    try:
        if scheduler.running:
            scheduler.shutdown()

        fetch_daily_trades_scheduler()
        return {"message": "Scheduler restarted successfully"}
    except Exception as e:
        error_msg = f"Error restarting scheduler: {str(e)}"
        logger.error(error_msg)
        raise HTTPException(status_code=500, detail=error_msg) from e

@router.get("/", response_model=TradesResponse)
async def get_trade_summary_endpoint(
    trade_date: str = Query(None, description="Trade date in YYYY-MM-DD format"),
    page: int = Query(1, description="Page number", ge=1),
    limit: int = Query(50, description="Items per page", ge=1, le=100)
):
    """Get the trade summary for a given date.

    Args:
        trade_date: The date to get the trade summary for in YYYY-MM-DD format
        page: Page number for pagination (default: 1)
        limit: Number of items per page (default: 50, max: 100)

    Returns:
        TradesResponse: Trade summary for the given date with pagination info
    """
    try:
        date_obj = None
        if not trade_date:
            date_obj = datetime.now()
            trade_date = date_obj.strftime("%Y-%m-%d")
        else:
            # Convert string to datetime
            date_obj = datetime.strptime(trade_date, "%Y-%m-%d")

        trade_summary = await get_trades(date_obj, page=page, limit=limit)
        return trade_summary
    except ValueError as e:
        raise HTTPException(status_code=400, detail=f"Invalid date format: {str(e)}") from e
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e)) from e


@router.get("/most-active-trades", response_model=list[MostActiveTrade])
async def get_most_active_trades_endpoint():
    """Get the most active trades for the current date.

    Returns:
        List[MostActiveTrade]: List of most active trades
    """
    try:
        return await get_most_active_trades()
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e)) from e

@router.get("/top-gainers-losers", response_model=TopGainersLosersResponse)
async def get_top_gainers_losers_endpoint(
    date: str = Query(..., description="Date in YYYY-MM-DD format")
):
    """Get the top gainers and losers for a specific date.

    Args:
        date: The date to get the top gainers and losers for in YYYY-MM-DD format

    Returns:
        List[MostActiveTrade]: List of top gainers and losers
    """
    try:
        return await get_top_gainers_losers(date)
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e)) from e

@router.get("/fetch-index-data")
async def fetch_index_data_endpoint():
    """Fetch and store market index data (ASPI and S&PSL20).

    This endpoint manually triggers the index data fetch job.
    """
    logger.info("Manual trigger: Starting market index data fetch")
    try:
        result = await manual_fetch_index_data()

        if result.get("success"):
            logger.info("Manual trigger: Successfully fetched and stored market index data")
        else:
            logger.error(f"Manual trigger: Market index data fetch failed - {result.get('message')}")

        logger.info("Manual trigger: Completed market index data fetch")
        return result
    except Exception as e:
        error_msg = f"Error fetching market index data: {str(e)}"
        logger.error(error_msg)
        raise HTTPException(status_code=500, detail=error_msg) from e

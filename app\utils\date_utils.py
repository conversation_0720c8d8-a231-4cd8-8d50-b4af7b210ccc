"""
Date Utilities

This module provides utility functions for handling dates consistently across the application.
"""

import logging
from datetime import datetime, date
from typing import Optional, Union, Tuple, Dict, Any
from zoneinfo import ZoneInfo

# Configure logging
logger = logging.getLogger(__name__)

# Standard date format for the application
DATE_FORMAT = "%Y-%m-%d"

def parse_date_string(date_str: Optional[str]) -> Optional[datetime]:
    """
    Parse a date string into a datetime object.

    Args:
        date_str: Date string in YYYY-MM-DD format or ISO format (YYYY-MM-DDThh:mm:ss.ssssss)

    Returns:
        Datetime object or None if parsing fails
    """
    if not date_str:
        return None

    try:
        # First try to parse as standard date format
        return datetime.strptime(date_str, DATE_FORMAT)
    except ValueError:
        # If that fails, try ISO format
        try:
            # Handle ISO format with T separator
            if 'T' in date_str:
                # Extract just the date part if there's a T separator
                date_part = date_str.split('T')[0]
                return datetime.strptime(date_part, DATE_FORMAT)
            # Try other common formats as a fallback
            for fmt in ["%Y-%m-%d %H:%M:%S", "%Y-%m-%d %H:%M:%S.%f"]:
                try:
                    return datetime.strptime(date_str, fmt)
                except ValueError:
                    continue
            # If we get here, none of our formats worked
            logger.error(f"Error parsing date string '{date_str}': unsupported format")
            return None
        except Exception as e:
            logger.error(f"Error parsing date string '{date_str}': {str(e)}")
            return None

def format_date(dt: Optional[Union[datetime, date]]) -> Optional[str]:
    """
    Format a datetime or date object as a string.

    Args:
        dt: Datetime or date object

    Returns:
        Formatted date string in YYYY-MM-DD format or None if input is None
    """
    if not dt:
        return None

    try:
        # If it's a datetime, convert to date
        if isinstance(dt, datetime):
            dt = dt.date()

        # Format the date
        return dt.strftime(DATE_FORMAT)
    except Exception as e:
        logger.error(f"Error formatting date {dt}: {str(e)}")
        return None

def validate_date_range(from_date: Optional[str], to_date: Optional[str]) -> Tuple[str, str]:
    """
    Validate a date range and ensure both dates are in the correct format.

    Args:
        from_date: Start date string in YYYY-MM-DD format
        to_date: End date string in YYYY-MM-DD format

    Returns:
        Tuple of validated from_date and to_date strings

    Raises:
        ValueError: If dates cannot be parsed or are invalid
    """
    # Parse dates
    from_dt = parse_date_string(from_date)
    to_dt = parse_date_string(to_date)

    # If to_date is not provided, use current date
    if not to_dt:
        to_dt = datetime.now()

    # If from_date is not provided, use 1 year before to_date
    if not from_dt:
        from_dt = datetime(to_dt.year - 1, to_dt.month, to_dt.day)

    # Ensure from_date is before to_date
    if from_dt > to_dt:
        logger.warning(f"From date {from_date} is after to date {to_date}. Swapping dates.")
        from_dt, to_dt = to_dt, from_dt

    # Format dates back to strings
    from_str = format_date(from_dt)
    to_str = format_date(to_dt)

    # Ensure we have valid strings
    if not from_str or not to_str:
        raise ValueError("Failed to format dates after validation")

    return from_str, to_str

def get_mongodb_date_query(from_date: Optional[str], to_date: Optional[str], date_field: str = "dateStr") -> dict:
    """
    Generate a MongoDB query for filtering by date range.

    Args:
        from_date: Start date string in YYYY-MM-DD format
        to_date: End date string in YYYY-MM-DD format
        date_field: Name of the date field in the MongoDB collection

    Returns:
        MongoDB query dictionary
    """
    # Validate date range
    from_date, to_date = validate_date_range(from_date, to_date)

    # Build query
    query = {}
    if from_date or to_date:
        query[date_field] = {}

        if from_date:
            query[date_field]["$gte"] = from_date

        if to_date:
            query[date_field]["$lte"] = to_date

    return query

def is_weekend(date_obj: datetime) -> bool:
    """
    Check if a given date is a weekend (Saturday or Sunday).

    Args:
        date_obj: The datetime object to check

    Returns:
        True if the date is a weekend, False otherwise
    """
    # weekday() returns 0-6 (Monday is 0, Sunday is 6)
    # So 5 and 6 are Saturday and Sunday
    return date_obj.weekday() >= 5

def skip_if_weekend(date_obj: datetime, service_name: str, force: bool = False) -> Optional[Dict[str, Any]]:
    """
    Check if a given date is a weekend and return a standardized skip response if it is.

    Args:
        date_obj: The datetime object to check
        service_name: Name of the service for logging and response messages
        force: If True, ignore weekend check and return None

    Returns:
        A standardized skip response dictionary if it's a weekend and force is False,
        None otherwise
    """
    if force:
        return None

    if is_weekend(date_obj):
        logger.info(f"Skipping {service_name} as it's a weekend")
        return {
            "success": True,
            "message": f"Skipped {service_name} (weekend)",
            "timestamp": date_obj.isoformat()
        }

    return None

"""
Utility functions for working with <PERSON> authentication.
"""

import os
import base64
import json
from typing import Optional, Dict, Any
from fastapi import Request
from clerk_backend_api import Clerk
import logging

logger = logging.getLogger(__name__)

async def get_user_id_from_request(request: Request) -> Optional[str]:
    """
    Get the user ID from a request using the JWT token.

    Args:
        request: The FastAPI request object

    Returns:
        The user ID if authenticated, None otherwise
    """
    # Get the JWT token from the Authorization header
    auth_header = request.headers.get('Authorization')
    if not auth_header or not auth_header.startswith('Bearer '):
        return None

    jwt_token = auth_header.split(' ')[1]

    try:
        # Split the JWT token into parts
        parts = jwt_token.split('.')
        if len(parts) != 3:
            return None

        # Decode the payload (second part)
        payload = parts[1]
        # Add padding if needed
        payload += '=' * (4 - len(payload) % 4)
        decoded_payload = base64.b64decode(payload)
        claims = json.loads(decoded_payload)

        # Extract the user ID from the 'sub' claim
        if 'sub' in claims:
            return str(claims['sub'])
        return None
    except Exception as e:
        logger.error(f"Error getting user ID from request: {str(e)}")
        return None

async def get_user_from_clerk(user_id: str) -> Optional[Dict[str, Any]]:
    """
    Get user information directly from Clerk.

    Args:
        user_id: The Clerk user ID

    Returns:
        User information if found, None otherwise
    """
    try:
        with Clerk(bearer_auth=os.getenv('CLERK_SECRET_KEY')) as clerk:
            user = clerk.users.get(user_id=user_id)
            if user:
                # Convert the user object to a dictionary
                return dict(user)
            return None
    except Exception as e:
        logger.error(f"Error fetching user from Clerk: {str(e)}")
        return None

async def get_user_from_request(request: Request) -> Optional[Dict[str, Any]]:
    """
    Get user information from a request by first getting the user ID
    and then fetching the user details from Clerk.

    Args:
        request: The FastAPI request object

    Returns:
        User information if authenticated, None otherwise
    """
    user_id = await get_user_id_from_request(request)
    if not user_id:
        return None

    return await get_user_from_clerk(user_id)
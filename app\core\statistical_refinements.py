"""
Statistical Refinements Module

This module provides statistical techniques to improve the robustness of financial calculations,
including winsorization to handle outliers and Ledoit-Wolf shrinkage for improved covariance estimation.
"""

import pandas as pd
import logging
from scipy.stats.mstats import winsorize
from sklearn.covariance import LedoitWolf

# Configure logging
logger = logging.getLogger(__name__)

# Fixed winsorization level at 2.5% (2.5th and 97.5th percentiles)
WINSORIZATION_LEVEL = 0.025

def winsorize_returns(returns_data: pd.DataFrame) -> pd.DataFrame:
    """
    Apply 2.5% winsorization to limit the influence of outliers in returns data.

    Winsorization replaces extreme values with less extreme values:
    - Values below the 2.5th percentile are replaced with the 2.5th percentile value
    - Values above the 97.5th percentile are replaced with the 97.5th percentile value

    This is applied to each asset's return series independently using scipy.stats.mstats.winsorize.

    Args:
        returns_data: DataFrame containing returns data (each column is a different asset)

    Returns:
        DataFrame with winsorized returns
    """
    # Apply winsorization to each column using scipy's winsorize function
    # limits=(0.025, 0.025) means 2.5% from each tail
    winsorized_data = returns_data.apply(lambda x: winsorize(x, limits=(WINSORIZATION_LEVEL, WINSORIZATION_LEVEL)), axis=0)

    # Log the winsorization
    logger.info(f"Applied {WINSORIZATION_LEVEL:.1%} winsorization to returns data")

    return winsorized_data

def calculate_ledoit_wolf_covariance(returns_data: pd.DataFrame) -> pd.DataFrame:
    """
    Calculate a robust covariance matrix using Ledoit-Wolf shrinkage.

    The Ledoit-Wolf estimator is a shrinkage method that combines the sample covariance matrix
    with a structured estimator (typically a diagonal matrix with the sample variances).
    It automatically determines the optimal shrinkage intensity to minimize the expected quadratic loss.

    This approach is particularly valuable in portfolio optimization because:
    1. It reduces estimation error in the covariance matrix
    2. It improves the conditioning of the matrix, making optimization more stable
    3. It works well even when the number of assets is large relative to the number of observations

    Args:
        returns_data: DataFrame containing returns data (each column is a different asset)

    Returns:
        DataFrame containing the robust covariance matrix
    """
    try:
        # Initialize the Ledoit-Wolf estimator
        lw = LedoitWolf()

        # Fit the estimator to the returns data
        lw.fit(returns_data)

        # Get the covariance matrix
        cov_matrix = lw.covariance_

        # Convert to DataFrame with proper index and columns
        cov_df = pd.DataFrame(cov_matrix, index=returns_data.columns, columns=returns_data.columns)

        # Log the shrinkage coefficient
        logger.info(f"Applied Ledoit-Wolf shrinkage with coefficient: {lw.shrinkage_:.4f}")

        return cov_df

    except Exception as e:
        logger.warning(f"Ledoit-Wolf estimation failed: {str(e)}. Falling back to sample covariance.")
        return returns_data.cov()

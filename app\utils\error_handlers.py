"""
Error Handlers

This module provides general error handling utilities for the API.
"""

import logging
from typing import Optional
from fastapi import HTTPException

from app.models.error_models import (
    ErrorTypes,
    create_error_response
)

# Configure logging
logger = logging.getLogger(__name__)

class APIError(Exception):
    """Custom exception for API errors."""
    def __init__(
        self,
        detail: str,
        status_code: int,
        error_code: str,
        explanation: Optional[str] = None,
        recommendation: Optional[str] = None,
        suggestion: Optional[str] = None
    ):
        self.detail = detail
        self.status_code = status_code
        self.error_code = error_code
        self.explanation = explanation
        self.recommendation = recommendation
        self.suggestion = suggestion
        super().__init__(detail)

def handle_error(error: APIError, log_error: bool = True) -> HTTPException:
    """
    Handle API errors and convert them to HTTP exceptions.

    Args:
        error: The APIError instance to handle
        log_error: Whether to log the error (default: True)

    Returns:
        HTTPException with appropriate status code and error details
    """
    if log_error:
        logger.error(f"API Error: {error.detail} (Code: {error.error_code})")

    # Special handling for target return errors
    if error.error_code == "INEFFICIENT_TARGET_RETURN":
        return HTTPException(
            status_code=error.status_code,
            detail={
                "error_code": "INEFFICIENT_TARGET_RETURN",
                "detail": "Inefficient Target Return!",
                "explanation": "Your target return is below the lowest optimal target return for the specified companies and constraints",
                "fix": error.recommendation
            }
        )
    elif error.error_code == "UNACHIEVABLE_TARGET_RETURN":
        return HTTPException(
            status_code=error.status_code,
            detail={
                "error_code": "UNACHIEVABLE_TARGET_RETURN",
                "detail": "Target Return Unachievable!",
                "explanation": "Your target return is above the maximum optimal target return for the specified companies and constraints",
                "fix": error.recommendation
            }
        )

    # Map error codes to ErrorTypes
    error_type_map = {
        "INVALID_PORTFOLIO_ID": ErrorTypes.INVALID_PORTFOLIO_ID,
        "PORTFOLIO_NOT_FOUND": ErrorTypes.PORTFOLIO_NOT_FOUND,
        "BENCHMARK_DATA_NOT_FOUND": ErrorTypes.BENCHMARK_DATA_NOT_FOUND,
        "PERFORMANCE_HISTORY_NOT_FOUND": ErrorTypes.PERFORMANCE_HISTORY_NOT_FOUND,
        "UPDATE_FAILED": ErrorTypes.UPDATE_FAILED,
        "REBALANCE_FAILED": ErrorTypes.REBALANCE_FAILED,
        "REBALANCE_CHECK_FAILED": ErrorTypes.REBALANCE_CHECK_FAILED,
        "DELETION_FAILED": ErrorTypes.DELETION_FAILED
    }

    # Get the appropriate error type or default to SERVER_ERROR
    error_type = error_type_map.get(error.error_code, ErrorTypes.SERVER_ERROR)

    # Create error response
    error_response = create_error_response(
        issue=error_type,
        description=error.detail,
        fix="Please try again later or contact support if the problem persists.",
        status_code=error.status_code,
        explanation=error.explanation,
        recommendation=error.recommendation,
        suggestion=error.suggestion
    )

    return HTTPException(
        status_code=error.status_code,
        detail=error_response
    )
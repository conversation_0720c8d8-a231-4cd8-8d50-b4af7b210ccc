"""
Portfolio Controller

This module provides API endpoints for portfolio optimization using Modern Portfolio Theory.
"""

import logging
from typing import Optional, Dict, Any, List
from datetime import datetime, timedelta
from fastapi import APIRouter, HTTPException, status, Request, Query

from app.services.company_service import get_sp_sl20_companies
from app.utils.clerk_utils import get_user_id_from_request
from app.utils.type_convert import convert_decimal_to_float
from app.services.user_service import UserService
from app.utils.date_utils import validate_date_range, format_date
from app.utils import APIError, handle_error, handle_portfolio_creation_error

# Import from core and services
from app.core.optimisation_engine import OptimisationEngine
from app.services.portfolio_service import (
    create_min_variance_portfolio,
    create_max_sharpe_portfolio,
    create_custom_portfolio,
    get_portfolio_sector_allocations,
    get_portfolio_performance_history
)
from app.services.portfolio_storage_service import (
    save_portfolio,
    get_portfolio,
    get_all_portfolios,
    delete_portfolio
)
from app.services.portfolio_update_service import manual_update_portfolios
from app.services.portfolio_rebalance_service import (
    manual_rebalance_portfolio,
    manual_check_portfolios_for_rebalancing
)
from app.services.benchmark_service import get_benchmark_performance_data
from app.models.portfolio_models import (
    PortfolioCreationRequest,
    PortfolioCreationResponse,
    PortfolioListResponse,
    PortfolioRequest,
    PortfolioResponse,
    PortfolioAnalysisResponse,
    SectorAllocationsResponse,
    PortfolioPerformanceHistoryResponse,
)
from app.models.benchmark_models import (
    BenchmarkPerformanceResponse
)
from app.config import (
    PORTFOLIO_TYPE_MIN_VARIANCE,
    PORTFOLIO_TYPE_MAX_SHARPE,
    PORTFOLIO_TYPE_CUSTOM
)
from app.config import (
    DEFAULT_START_DATE,
    DEFAULT_END_DATE,
    DEFAULT_RISK_FREE_RATE,
    DEFAULT_WEIGHT_BOUNDS
)

# Configure logging
logger = logging.getLogger(__name__)

router = APIRouter(tags=["Portfolio Optimization"])


def validate_custom_portfolio_request(request: PortfolioCreationRequest) -> None:
    """
    Centralized validation function for custom portfolio requests.

    This function validates all required parameters for custom portfolios and
    performs additional validation on parameter values.

    Args:
        request: The portfolio creation request to validate

    Raises:
        ValueError: If any validation fails
    """
    # 1. Validate custom_companies
    if not request.custom_companies:
        raise ValueError("No companies selected for custom portfolio. Please select companies.")

    # 2. Validate minimum number of companies
    if len(request.custom_companies) < 5:
        raise ValueError("At least 5 companies must be selected for optimal portfolio diversification.")

    # 3. Validate date range
    if request.from_date is None or request.to_date is None:
        raise ValueError("Date range (from_date and to_date) must be provided for custom portfolios.")

    # 4. Validate risk_free_rate
    if request.risk_free_rate is None:
        raise ValueError("Risk-free rate is required for custom portfolios.")
    elif request.risk_free_rate < -0.05 or request.risk_free_rate > 0.15:
        raise ValueError("Risk-free rate must be between -5% and 15% (-0.05 to 0.15 in decimal form).")

    # 5. Validate constraint_set
    if request.constraint_set is None:
        raise ValueError("Constraint set is required for custom portfolios.")
    elif len(request.constraint_set) != 2:
        raise ValueError("Constraint set must contain exactly two values: [min_weight, max_weight].")
    elif request.constraint_set[0] < 0:
        raise ValueError("Minimum weight cannot be negative.")
    elif request.constraint_set[1] > 1:
        raise ValueError("Maximum weight cannot exceed 1.0 (100%).")
    elif request.constraint_set[0] > request.constraint_set[1]:
        raise ValueError("Minimum weight cannot be greater than maximum weight.")

    # 6. Validate target_return
    if request.target_return is None:
        raise ValueError("Target return is required for custom portfolios.")
    elif request.target_return < 0:
        raise ValueError("Target return cannot be negative.")
    # Removed static maximum limit - let the efficient frontier validation handle maximum limits
    # based on actual market data and selected companies

    # 7. Validate rebalance_frequency
    if request.rebalance_frequency is None:
        raise ValueError("Rebalance frequency is required for custom portfolios.")
    elif request.rebalance_frequency not in ["MONTHLY", "QUARTERLY", "SEMI-ANNUALLY", "ANNUALLY"]:
        raise ValueError("Rebalance frequency must be one of: MONTHLY, QUARTERLY, SEMI-ANNUALLY, ANNUALLY.")


# -------------GET REQUESTS-------------

@router.get("/api/portfolios",
            summary="Get all portfolios",
            description="Returns a list of all portfolios.",
            response_description="List of portfolios",
            response_model=PortfolioListResponse)
async def get_portfolios_endpoint(request: Request) -> Dict[str, List[Dict[str, Any]]]:
    """
    Get all portfolios for the authenticated user.

    Args:
        request: The FastAPI request object containing authentication information

    Returns:
        Dict containing a list of portfolios

    Raises:
        HTTPException: If portfolio retrieval fails or user is not authenticated
    """
    try:
        # Get user ID from request
        user_id = await get_user_id_from_request(request)
        if not user_id:
            raise HTTPException(
                status_code=401,
                detail={
                    "issue": "unauthorized",
                    "description": "User not authenticated",
                    "fix": "Please log in to view portfolios"
                }
            )

        # Get user's portfolio IDs
        portfolio_ids = await UserService.get_user_portfolios(user_id) or []

        # Fetch only the portfolios that belong to the user
        all_portfolios = await get_all_portfolios()
        # Transform the data for the response
        transformed_portfolios = []
        for portfolio in all_portfolios:
            # Skip portfolios that don't belong to the user
            if portfolio.get("portfolioId") not in portfolio_ids:
                continue

            # Convert Decimal128 to float for the response
            transformed = {
                "portfolio_id": portfolio.get("portfolioId"),
                "portfolio_name": portfolio.get("portfolioName"),
                "portfolio_type": portfolio.get("portfolioType"),
                "total_investment_amount": convert_decimal_to_float(portfolio.get("totalInvestmentAmount")),
                "total_market_value": convert_decimal_to_float(portfolio.get("totalMarketValue")),
                "unrealized_gain_loss": convert_decimal_to_float(portfolio.get("totalUnrealizedGainLoss")),
                "annualized_return": convert_decimal_to_float(portfolio.get("annualizedReturn")),
                "annualized_volatility": convert_decimal_to_float(portfolio.get("annualizedVolatility")),
                "creation_date": portfolio.get("creationDate"),
                "rebalance_frequency": portfolio.get("rebalanceFrequency"),
                "last_rebalanced_date": portfolio.get("lastRebalancedDate"),
                "last_updated_date": portfolio.get("lastUpdatedDate"),
                "holdings_count": len(portfolio.get("holdings", [])),
                "cash_component": convert_decimal_to_float(portfolio.get("cashComponent"))
            }
            transformed_portfolios.append(transformed)

        return {"portfolios": transformed_portfolios}

    except HTTPException:
        # Re-raise HTTP exceptions
        raise
    except Exception as e:
        # Handle errors
        raise HTTPException(status_code=500, detail=f"An error occurred while retrieving portfolios: {str(e)}") from e



@router.get("/api/portfolios/{portfolio_id}",
            summary="Get portfolio details",
            description="Returns details of a specific portfolio.",
            response_description="Portfolio details",
            response_model=PortfolioResponse)
async def get_portfolio_details(request: Request, portfolio_id: str):
    """
    Get details of a specific portfolio.

    Args:
        portfolio_id: Portfolio ID

    Returns:
        Portfolio details

    Raises:
        HTTPException: If portfolio retrieval fails
    """
    try:
        # Get user ID from request
        user_id = await get_user_id_from_request(request)
        if not user_id:
            raise HTTPException(
                status_code=401,
                detail={
                    "issue": "unauthorized",
                    "description": "User not authenticated",
                    "fix": "Please log in to get portfolio details"
                }
            )

        # Ensure the caller owns this portfolio
        portfolio_ids = await UserService.get_user_portfolios(user_id) or []
        if portfolio_id not in portfolio_ids:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail={
                    "issue": "forbidden",
                    "description": "You are not authorised to access this portfolio.",
                    "fix": "Request only portfolios that you own."
                }
            )

        portfolio = await get_portfolio(portfolio_id)

        if not portfolio:
            raise HTTPException(status_code=404, detail=f"Portfolio with ID {portfolio_id} not found")

        # Transform the data for the response
        transformed_portfolio = {
            "portfolio_id": portfolio.get("portfolioId"),
            "portfolio_name": portfolio.get("portfolioName"),
            "portfolio_type": portfolio.get("portfolioType"),
            "total_investment_amount": convert_decimal_to_float(
                portfolio.get("totalInvestmentAmount")),
            "total_market_value": convert_decimal_to_float(
                portfolio.get("totalMarketValue")),
            "unrealized_gain_loss": convert_decimal_to_float(
                portfolio.get("totalUnrealizedGainLoss")),
            "annualized_return": convert_decimal_to_float(
                portfolio.get("annualizedReturn")),
            "annualized_volatility": convert_decimal_to_float(
                portfolio.get("annualizedVolatility")),
            "creation_date": portfolio.get("creationDate"),
            "rebalance_frequency": portfolio.get("rebalanceFrequency"),
            "last_rebalanced_date": portfolio.get("lastRebalancedDate"),
            "last_updated_date": portfolio.get("lastUpdatedDate"),
            "cash_component": convert_decimal_to_float(portfolio.get("cashComponent")),
            "holdings": []
        }

        # Transform holdings
        for holding in portfolio.get("holdings", []):
            transformed_holding = {
                "company_name": holding.get("companyName"),
                "ticker_symbol": holding.get("tickerSymbol"),
                "quantity": convert_decimal_to_float(holding.get("quantity")),
                "last_traded_price": convert_decimal_to_float(holding.get("lastTradedPrice")),
                "total_cost": convert_decimal_to_float(holding.get("totalCost")),
                "market_value": convert_decimal_to_float(holding.get("marketValue")),
                "unrealized_gain_loss": convert_decimal_to_float(holding.get("unrealizedGainLoss")),
                "weight": convert_decimal_to_float(holding.get("weight")),
                "annualized_return": convert_decimal_to_float(holding.get("annualizedReturn")),
                "annualized_volatility": convert_decimal_to_float(
                    holding.get("annualizedVolatility"))
            }
            transformed_portfolio["holdings"].append(transformed_holding)

        return {"portfolio": transformed_portfolio}

    except HTTPException:
        # Re-raise HTTP exceptions
        raise
    except Exception as e:
        # Handle errors
        raise HTTPException(status_code=500,
                detail=f"An error occurred while retrieving portfolio details: {str(e)}") from e


@router.get("/api/portfolios/{portfolio_id}/sector-allocations",
            summary="Get portfolio sector allocations",
            description="Returns the sector allocations for a specific portfolio.",
            response_description="Portfolio sector allocations",
            response_model=SectorAllocationsResponse)
async def get_sector_allocations(portfolio_id: str):
    """
    Get sector allocations for a specific portfolio.

    This endpoint calculates what percentage of the portfolio's total market value
    is allocated to each sector.

    Args:
        portfolio_id: Portfolio ID

    Returns:
        Portfolio sector allocations

    Raises:
        HTTPException: If sector allocation calculation fails
    """
    try:
        # Get sector allocations from the service
        sector_allocations = await get_portfolio_sector_allocations(portfolio_id)
        return sector_allocations

    except ValueError as ve:
        # Handle not found or validation errors
        raise HTTPException(status_code=404, detail=str(ve)) from ve

    except Exception as e:
        # Handle other errors
        raise HTTPException(status_code=500,
                detail=f"An error occurred while calculating sector allocations: {str(e)}") from e



# -------------POST REQUESTS-------------

@router.post("/api/portfolios",
            summary="Create a portfolio",
            description="Creates a portfolio based on the specified type and constraints.",
            response_description="Portfolio details including allocations and performance metrics",
            response_model=PortfolioCreationResponse)
async def create_portfolio(request: Request, portfolio_request: PortfolioCreationRequest):
    """
    Creates a portfolio based on the specified type and constraints.

    This endpoint supports three types of portfolios:
    - Minimum Variance Portfolio (lowest possible volatility)
    - Maximum Sharpe Ratio Portfolio (optimal risk-adjusted return)
    - Custom Portfolio (user-defined constraints)

    Args:
        request: The FastAPI request object
        portfolio_request: Portfolio creation request containing portfolio type and constraints

    Returns:
        Portfolio details including allocations and performance metrics

    Raises:
        HTTPException: If portfolio creation fails
    """
    try:
        # Get user ID from request
        user_id = await get_user_id_from_request(request)
        if not user_id:
            raise HTTPException(
                status_code=401,
                detail={
                    "issue": "unauthorized",
                    "description": "User not authenticated",
                    "fix": "Please log in to create a portfolio"
                }
            )

        logger.debug("Request: %s", portfolio_request)
        # Extract constraints from the request
        constraints = {}
        if portfolio_request.from_date:
            constraints["from_date"] = portfolio_request.from_date
            constraints["start_date"] = portfolio_request.from_date  # Keep for backward compatibility
        if portfolio_request.to_date:
            constraints["to_date"] = portfolio_request.to_date
            constraints["end_date"] = portfolio_request.to_date  # Keep for backward compatibility
        if portfolio_request.risk_free_rate is not None:
            constraints["risk_free_rate"] = portfolio_request.risk_free_rate
        if portfolio_request.constraint_set:
            constraints["weight_bounds"] = (portfolio_request.constraint_set[0], portfolio_request.constraint_set[1])

        # Add excluded companies to constraints if provided
        if portfolio_request.excluded_companies:
            constraints["excluded_companies"] = portfolio_request.excluded_companies

        # Create the portfolio based on the specified type
        if portfolio_request.portfolio_type == PORTFOLIO_TYPE_MIN_VARIANCE:
            # Create a minimum variance portfolio
            portfolio = await create_min_variance_portfolio(constraints)

            # Extract allocations and performance directly from the response
            min_variance_allocations = portfolio.get("allocations", {})

            # Log the allocations for debugging
            logger.debug("Min variance allocations: %s", min_variance_allocations)

            # Filter out zero weights and convert percentages to decimals
            allocations = {ticker: weight / 100 for ticker, weight in min_variance_allocations.items() if weight > 0}

            # Log the processed allocations
            logger.debug("Processed allocations: %s", allocations)

            performance = portfolio.get("performance", {})

        elif portfolio_request.portfolio_type == PORTFOLIO_TYPE_MAX_SHARPE:
            # Create a maximum Sharpe ratio portfolio
            portfolio = await create_max_sharpe_portfolio(constraints)

            # Extract allocations and performance directly from the response
            max_sharpe_allocations = portfolio.get("allocations", {})

            # Log the allocations for debugging
            logger.debug("Max sharpe allocations: %s", max_sharpe_allocations)

            # Filter out zero weights and convert percentages to decimals
            allocations = {ticker: weight / 100 for ticker, weight in max_sharpe_allocations.items() if weight > 0}

            # Log the processed allocations
            logger.debug("Processed allocations: %s", allocations)

            performance = portfolio.get("performance", {})

        elif portfolio_request.portfolio_type == PORTFOLIO_TYPE_CUSTOM:
            logger.info("Request: %s", portfolio_request)

            # Validate custom portfolio parameters using a centralized function
            validate_custom_portfolio_request(portfolio_request)

            # Ensure custom_companies is not None
            if not portfolio_request.custom_companies:
                raise ValueError("No companies selected for custom portfolio")

            # Create a custom portfolio with the selected companies and target return
            portfolio = await create_custom_portfolio(
                companies_dict=portfolio_request.custom_companies,
                constraints=constraints,
                target_return=portfolio_request.target_return if portfolio_request.target_return is not None else 0.0
            )

            # Extract allocations and performance
            custom_allocations = portfolio.get("allocations", {})

            # Filter out zero weights and convert percentages to decimals
            allocations = {ticker: weight / 100 for ticker, weight in custom_allocations.items() if weight > 0}

            performance = portfolio.get("performance", {})

        else:
            # Invalid portfolio type
            raise ValueError(f"Invalid portfolio type: {portfolio_request.portfolio_type}")

        # Add a safety check to ensure we have allocations
        if not allocations:
            logger.warning("No allocations with positive weights found. This is unusual and may indicate an issue with the optimization.")

            # Get the original allocations
            original_allocations = portfolio.get("allocations", {})

            # Use the original allocations but with very small weights to avoid the error
            # Only include allocations with non-zero weights
            allocations = {ticker: 0.0001 for ticker, weight in original_allocations.items() if weight > 0}

            # If still empty, include all allocations
            if not allocations:
                allocations = {ticker: 0.0001 for ticker in original_allocations.keys() if ticker}

            # Normalize to ensure sum is 1.0
            total_weight = sum(allocations.values())
            if total_weight > 0:
                allocations = {ticker: weight / total_weight for ticker, weight in allocations.items()}

            logger.info("Created fallback allocations with %d assets", len(allocations))

        # Save the portfolio to the database
        portfolio_id = await save_portfolio(
            portfolio_type=portfolio_request.portfolio_type,
            portfolio_name=portfolio_request.portfolio_name,
            investment_amount=portfolio_request.investment_amount,
            allocations=allocations,
            performance=performance,
            rebalance_frequency=portfolio_request.rebalance_frequency,
            constraints=constraints
        )

        if portfolio_id:
            # Add the portfolio to the user's portfolios
            await UserService.add_portfolio_to_user(user_id, portfolio_id)

            # Add the portfolio ID and user ID to the response
            portfolio["portfolio_id"] = portfolio_id
            portfolio["user_id"] = user_id
            portfolio["message"] = f"Portfolio '{portfolio_request.portfolio_name}' created successfully"
        else:
            portfolio["message"] = "Portfolio optimization succeeded but saving to database failed"

        # Return the portfolio
        return portfolio

    except Exception as e:
        # Use the specialized error handler for portfolio creation errors
        raise handle_portfolio_creation_error(e) from e


@router.post("/api/portfolios/analyze",
            summary="Analyze and optimize portfolio",
            description='''Analyzes a portfolio of stocks and returns optimization results
            including maximum Sharpe ratio portfolio, minimum variance portfolio, and
            efficient frontier data. Supports weight constraints for individual assets.''',
            response_description='''Portfolio optimization results including allocation percentages
            and performance metrics''',
            response_model=PortfolioAnalysisResponse)
async def analyze_portfolio(portfolio_data: PortfolioRequest):
    """
    Analyzes a portfolio of stocks using Modern Portfolio Theory

    This endpoint calculates:
    - Maximum Sharpe ratio portfolio (optimal risk-adjusted return)
    - Minimum variance portfolio (lowest volatility)
    - Efficient frontier data points

    Supports constraints:
    - Asset-level weight constraints (min/max allocation per asset)

    Args:
        portfolio_data: Portfolio request containing companies, date range,
        risk-free rate, and optional constraints

    Returns:
        Dict containing optimization results with portfolio allocations and performance metrics

    Raises:
        HTTPException: If data retrieval or calculations fail
    """
    try:
        # Initialize the OptimisationEngine with the provided parameters
        engine = OptimisationEngine(
            companies_dict=portfolio_data.companies,
            from_date=portfolio_data.from_date,
            to_date=portfolio_data.to_date,
            risk_free_rate=portfolio_data.risk_free_rate,
            weight_bounds=(portfolio_data.constraint_set[0],
                        portfolio_data.constraint_set[1]
                        ) if portfolio_data.constraint_set else (0, 1)
        )

        # First, get the data (this is async and needs to be awaited)
        await engine.get_data()

        # Now get the portfolio optimization results
        portfolio_summary = engine.get_portfolio_summary()

        # Return the results
        return portfolio_summary
    except ValueError as ve:
        # Handle specific validation errors with structured response
        error_message = str(ve)
        raise HTTPException(status_code=400, detail={
            "issue": "validation_error",
            "description": error_message,
            "fix": "Please check your input parameters and try again."
        }) from ve
    except Exception as e:
        # Handle general errors with structured response
        raise HTTPException(status_code=500, detail={
            "issue": "analysis_error",
            "description": f"An error occurred during portfolio analysis: {str(e)}",
            "fix": "Please try again with different parameters or contact support."
        }) from e

# -------------PATCH REQUESTS-------------

@router.patch("/api/portfolios/rebalance-check",
            summary="Check all portfolios for rebalancing",
            description="Checks all portfolios to see if any need rebalancing based on their rebalance frequency.",
            response_description="Rebalance check operation result")
async def check_portfolios_for_rebalancing_endpoint(
    force_rebalance: bool = Query(False, description="Force rebalance even on weekends")
):
    """
    Check all portfolios to see if any need rebalancing.

    This endpoint triggers the portfolio rebalance check process, which:
    1. Checks all active portfolios to see if any need rebalancing based on their rebalance frequency
    2. Rebalances portfolios that need rebalancing
    3. Returns a summary of the rebalance check operation

    Args:
        force_rebalance: If True, rebalance even on weekends. Default is False.

    Returns:
        Dictionary containing rebalance check operation result

    Raises:
        HTTPException: If the rebalance check fails
    """
    try:
        result = await manual_check_portfolios_for_rebalancing(force_rebalance)

        if not result.get("success") and result.get("error"):
            raise HTTPException(
                status_code=500,
                detail={
                    "issue": "rebalance_check_failed",
                    "description": result.get('message', "Portfolio rebalance check failed"),
                    "fix": "Please try again later or contact support."
                }
            )

        return result
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail={
                "issue": "rebalance_check_error",
                "description": f"An error occurred during portfolio rebalance check: {str(e)}",
                "fix": "Please try again later or contact support if the problem persists."
            }
        ) from e

@router.patch("/api/portfolios/{portfolio_id}/rebalance",
            summary="Rebalance a portfolio or all portfolios",
            description="Rebalances a specific portfolio or all portfolios by recalculating optimal allocations and adjusting holdings.",
            response_description="Rebalance operation result")
async def rebalance_portfolio_endpoint(
    portfolio_id: Optional[str] = None,
    force_rebalance: bool = Query(False, description="Force rebalance even on weekends")
):
    """
    Rebalance a specific portfolio or all portfolios.

    This endpoint triggers the portfolio rebalancing process, which:
    1. Recalculates optimal allocations based on the portfolio type
    2. Adjusts holdings to match the new allocations
    3. Updates the portfolio document(s) in the database

    Args:
        portfolio_id: ID of the portfolio to rebalance. If None, all portfolios will be rebalanced.
        force_rebalance: If True, rebalance even on weekends. Default is False.

    Returns:
        Dictionary containing rebalance operation result

    Raises:
        HTTPException: If the portfolio doesn't exist or rebalance fails
    """
    try:
        # Validate portfolio_id format if provided (None means rebalance all portfolios)
        if portfolio_id is not None and not portfolio_id.startswith("PF-"):
            raise APIError(
                detail=f"Invalid portfolio ID format: {portfolio_id}",
                status_code=status.HTTP_400_BAD_REQUEST,
                error_code="INVALID_PORTFOLIO_ID"
            )

        result = await manual_rebalance_portfolio(portfolio_id, force_rebalance)

        if not result.get("success"):
            raise HTTPException(
                status_code=500,
                detail={
                    "issue": "rebalance_failed",
                    "description": result.get('message', "Portfolio rebalance failed"),
                    "fix": "Please try again later or contact support."
                }
            )

        return result
    except APIError as e:
        # Re-raise API errors as HTTP exceptions
        raise handle_error(e, log_error=True) from e
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail={
                "issue": "rebalance_error",
                "description": f"An error occurred during portfolio rebalance: {str(e)}",
                "fix": "Please try again later or contact support if the problem persists."
            }
        ) from e

@router.patch("/api/portfolios",
            summary="Update portfolios with latest market data",
            description="Updates all portfolios or a specific portfolio with the latest market data. Use portfolio_id=None to update all portfolios.",
            response_description="Update operation result")
async def update_portfolios_endpoint(portfolio_id: Optional[str] = None):
    """
    Update a specific portfolio or all portfolios with the latest market data.

    This endpoint triggers the portfolio update process, which:
    1. Fetches the latest market data for each holding
    2. Calculates new market values and unrealized gains/losses
    3. Updates the portfolio document(s) in the database

    Args:
        portfolio_id: ID of the portfolio to update. If None, all portfolios will be updated.

    Returns:
        Dictionary containing update operation result

    Raises:
        HTTPException: If the portfolio doesn't exist or update fails
    """
    try:
        # Validate portfolio_id format if provided (None means update all portfolios)
        if portfolio_id is not None and not portfolio_id.startswith("PF-"):
            raise APIError(
                detail=f"Invalid portfolio ID format: {portfolio_id}",
                status_code=status.HTTP_400_BAD_REQUEST,
                error_code="INVALID_PORTFOLIO_ID"
            )

        result = await manual_update_portfolios(portfolio_id)

        if not result.get("success"):
            raise HTTPException(
                status_code=500,
                detail={
                    "issue": "update_failed",
                    "description": result.get('message', "Portfolio update failed"),
                    "fix": "Please try again later or contact support."
                }
            )

        return result
    except APIError as e:
        # Re-raise API errors as HTTP exceptions
        raise handle_error(e, log_error=True) from e
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail={
                "issue": "update_error",
                "description": f"An error occurred during portfolio update: {str(e)}",
                "fix": "Please try again later or contact support if the problem persists."
            }
        ) from e

# -------------DELETE REQUESTS-------------

@router.delete("/api/portfolios/{portfolio_id}",
            summary="Delete a portfolio",
            description="Permanently deletes a portfolio by ID.",
            response_description="Deletion confirmation")
async def delete_portfolio_endpoint(request: Request, portfolio_id: str):
    """
    Delete a portfolio by ID.

    This endpoint permanently removes a portfolio from the database.
    This operation cannot be undone.

    Args:
        request: The FastAPI request object containing authentication information
        portfolio_id: ID of the portfolio to delete

    Returns:
        Dictionary containing deletion confirmation

    Raises:
        HTTPException: If the portfolio doesn't exist or deletion fails
    """
    try:
        # Get user ID from request
        user_id = await get_user_id_from_request(request)
        if not user_id:
            raise HTTPException(
                status_code=401,
                detail={
                    "issue": "unauthorized",
                    "description": "User not authenticated",
                    "fix": "Please log in to delete a portfolio"
                }
            )

        # Validate portfolio_id format
        if not portfolio_id or not portfolio_id.startswith("PF-"):
            raise APIError(
                detail=f"Invalid portfolio ID format: {portfolio_id}",
                status_code=status.HTTP_400_BAD_REQUEST,
                error_code="INVALID_PORTFOLIO_ID"
            )

        # First check if the portfolio exists
        portfolio = await get_portfolio(portfolio_id)
        if not portfolio:
            raise HTTPException(
                status_code=404,
                detail={
                    "issue": "portfolio_not_found",
                    "description": f"Portfolio with ID {portfolio_id} not found",
                    "fix": "Please check the portfolio ID and try again."
                }
            )

        # Remove portfolio ID from user's portfolios array
        await UserService.remove_portfolio_from_user(user_id, portfolio_id)

        # Delete the portfolio
        await delete_portfolio(portfolio_id)

        # Return success response
        return {
            "success": True,
            "message": f"Portfolio {portfolio_id} deleted successfully",
            "portfolio_id": portfolio_id
        }
    except HTTPException:
        # Re-raise HTTP exceptions
        raise
    except ValueError as ve:
        # Portfolio not found
        raise HTTPException(
            status_code=404,
            detail={
                "issue": "portfolio_not_found",
                "description": str(ve),
                "fix": "Please check the portfolio ID and try again."
            }
        ) from ve
    except RuntimeError as re:
        # Database operation failed
        raise HTTPException(
            status_code=500,
            detail={
                "issue": "deletion_failed",
                "description": str(re),
                "fix": "Please try again later or contact support if the problem persists."
            }
        ) from re
    except APIError as e:
        # Re-raise API errors as HTTP exceptions
        raise handle_error(e, log_error=True) from e
    except Exception as e:
        # Handle unexpected errors
        raise HTTPException(
            status_code=500,
            detail={
                "issue": "unexpected_error",
                "description": f"An unexpected error occurred: {str(e)}",
                "fix": "Please try again later or contact support if the problem persists."
            }
        ) from e


@router.get("/api/default-portfolios-constraints",
            summary="Get default constraints for portfolio creation",
            description="Returns the default constraints for a portfolio whether maximum sharpe or minimum variance",
            response_description="Default constraints")
async def get_default_constraints():
    """
    Get default constraints for portfolio creation.

    This endpoint returns the default constraints for a portfolio whether maximum sharpe or minimum variance.
    """
    # Get companies as dictionary
    companies_dict = await get_sp_sl20_companies()

    return {
        "start_date": DEFAULT_START_DATE,
        "end_date": DEFAULT_END_DATE,
        "risk_free_rate": DEFAULT_RISK_FREE_RATE,
        "weight_bounds": DEFAULT_WEIGHT_BOUNDS,
        "default_companies": companies_dict
    }

@router.get("/api/portfolios/{portfolio_id}/benchmark-performance",
            summary="Get benchmark performance data",
            description="Returns performance data comparing a portfolio against market indices",
            response_description="Benchmark performance data for charting",
            response_model=BenchmarkPerformanceResponse)
async def get_benchmark_performance_endpoint(
    portfolio_id: str,
    from_date: Optional[str] = Query(None, description="Start date for benchmark comparison (YYYY-MM-DD)"),
    to_date: Optional[str] = Query(None, description="End date for benchmark comparison (YYYY-MM-DD)"),
    benchmark_indices: Optional[str] = Query("ASPI,S&PSL20", description="Comma-separated list of indices to benchmark against")
):
    """
    Get benchmark performance data for a portfolio compared to market indices.

    This endpoint calculates the percentage change of the portfolio and selected indices
    from a common start date, allowing for visual comparison of performance.

    Args:
        portfolio_id: ID of the portfolio to benchmark
        from_date: Start date for benchmark comparison (YYYY-MM-DD)
        to_date: End date for benchmark comparison (YYYY-MM-DD)
        benchmark_indices: Comma-separated list of indices to benchmark against (default: "ASPI,S&PSL20")

    Returns:
        Dictionary containing benchmark performance data for charting

    Raises:
        HTTPException: If the portfolio doesn't exist or benchmark data cannot be generated
    """
    try:
        # Validate portfolio_id format
        if not portfolio_id.startswith("PF-"):
            raise APIError(
                detail=f"Invalid portfolio ID format: {portfolio_id}",
                status_code=status.HTTP_400_BAD_REQUEST,
                error_code="INVALID_PORTFOLIO_ID"
            )

        # Get portfolio to verify it exists
        portfolio = await get_portfolio(portfolio_id)
        if not portfolio:
            raise APIError(
                detail=f"Portfolio with ID {portfolio_id} not found",
                status_code=status.HTTP_404_NOT_FOUND,
                error_code="PORTFOLIO_NOT_FOUND"
            )

        # Use default dates if not provided
        if not from_date:
            # Default to portfolio creation date or 1 year ago, whichever is later
            creation_date = portfolio.get("creationDate")
            if creation_date:
                from_date = format_date(creation_date)
            else:
                from_date = format_date(datetime.now() - timedelta(days=365))

        if not to_date:
            # Default to current date
            to_date = format_date(datetime.now())

        # Validate and standardize date range
        from_date, to_date = validate_date_range(from_date, to_date)

        # Parse benchmark indices - ensure we have a default value
        indices_str = benchmark_indices or "ASPI,S&PSL20"
        indices_list = [index.strip() for index in indices_str.split(",") if index.strip()]
        if not indices_list:
            indices_list = ["ASPI", "S&PSL20"]  # Default indices if none provided

        # Get benchmark performance data
        result = await get_benchmark_performance_data(
            portfolio_id=portfolio_id,
            from_date=from_date,
            to_date=to_date,
            benchmark_indices=indices_list
        )

        if not result.get("success"):
            raise HTTPException(
                status_code=404,
                detail={
                    "issue": "benchmark_data_not_found",
                    "description": result.get("message", "Failed to generate benchmark data"),
                    "fix": "Try a different date range or ensure portfolio has value history data"
                }
            )

        # If there's a warning but the operation was successful, log it and include it in the response
        if result.get("success") and result.get("warning"):
            logger.warning(f"Benchmark performance warning: {result.get('warning')}")

            # Return the result with the warning
            return {
                **result,
                "warning": result.get("warning")
            }

        return result
    except APIError as e:
        # Re-raise API errors as HTTP exceptions
        raise handle_error(e, log_error=True) from e
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail={
                "issue": "benchmark_error",
                "description": f"An error occurred while generating benchmark data: {str(e)}",
                "fix": "Please try again later or contact support if the problem persists."
            }
        ) from e


@router.get("/api/portfolios/{portfolio_id}/performance-history",
            summary="Get portfolio performance history",
            description="Returns historical market value data for a portfolio over time",
            response_description="Portfolio performance history data for charting",
            response_model=PortfolioPerformanceHistoryResponse)
async def get_portfolio_performance_history_endpoint(
    request: Request,
    portfolio_id: str,
    from_date: Optional[str] = Query(None, description="Start date for performance history (YYYY-MM-DD)"),
    to_date: Optional[str] = Query(None, description="End date for performance history (YYYY-MM-DD)")
):
    """
    Get historical market value data for a portfolio over time.

    This endpoint retrieves the portfolio's market value history for the specified date range,
    which can be used to create performance charts showing how the portfolio value has changed over time.

    Args:
        request: The FastAPI request object containing authentication information
        portfolio_id: ID of the portfolio to get performance history for
        from_date: Optional start date for performance history (YYYY-MM-DD)
        to_date: Optional end date for performance history (YYYY-MM-DD)

    Returns:
        Dictionary containing portfolio performance history data for charting

    Raises:
        HTTPException: If the portfolio doesn't exist or performance history cannot be generated
    """
    try:
        # Get user ID from request
        user_id = await get_user_id_from_request(request)
        if not user_id:
            raise HTTPException(
                status_code=401,
                detail={
                    "issue": "unauthorized",
                    "description": "User not authenticated",
                    "fix": "Please log in to view portfolio performance history"
                }
            )

        # Ensure the caller owns this portfolio
        portfolio_ids = await UserService.get_user_portfolios(user_id) or []
        if portfolio_id not in portfolio_ids:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail={
                    "issue": "forbidden",
                    "description": "You are not authorized to access this portfolio.",
                    "fix": "Request only portfolios that you own."
                }
            )

        # Validate portfolio_id format
        if not portfolio_id.startswith("PF-"):
            raise APIError(
                detail=f"Invalid portfolio ID format: {portfolio_id}",
                status_code=status.HTTP_400_BAD_REQUEST,
                error_code="INVALID_PORTFOLIO_ID"
            )

        # Get portfolio performance history
        performance_history = await get_portfolio_performance_history(
            portfolio_id=portfolio_id,
            from_date=from_date,
            to_date=to_date
        )

        return performance_history

    except ValueError as ve:
        # Handle not found or validation errors
        raise HTTPException(
            status_code=404,
            detail={
                "issue": "performance_history_not_found",
                "description": str(ve),
                "fix": "Try a different date range or ensure portfolio has value history data"
            }
        ) from ve
    except APIError as e:
        # Re-raise API errors as HTTP exceptions
        raise handle_error(e, log_error=True) from e
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail={
                "issue": "performance_history_error",
                "description": f"An error occurred while retrieving performance history: {str(e)}",
                "fix": "Please try again later or contact support if the problem persists."
            }
        ) from e

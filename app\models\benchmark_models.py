"""
Benchmark Models

This module defines the data models for benchmark-related API requests and responses.
"""

from typing import Dict, List, Optional, Any
from datetime import datetime
from pydantic import BaseModel, Field

class BenchmarkDataPoint(BaseModel):
    """Model for a single data point in the benchmark chart."""
    date: str = Field(..., description="Date in YYYY-MM-DD format")
    portfolio_pct_change: float = Field(..., description="Portfolio percentage change from start date")
    aspi_pct_change: Optional[float] = Field(None, description="ASPI percentage change from start date")
    spsl20_pct_change: Optional[float] = Field(None, description="S&PSL20 percentage change from start date")

class BenchmarkPerformanceRequest(BaseModel):
    """Request model for benchmark performance data."""
    from_date: Optional[str] = Field(None, description="Start date for benchmark comparison (YYYY-MM-DD)")
    to_date: Optional[str] = Field(None, description="End date for benchmark comparison (YYYY-MM-DD)")
    benchmark_indices: List[str] = Field(["ASPI", "S&PSL20"], description="List of indices to benchmark against")

class BenchmarkPerformanceResponse(BaseModel):
    """Response model for benchmark performance data."""
    success: bool = Field(..., description="Whether the request was successful")
    portfolio_id: Optional[str] = Field(None, description="ID of the portfolio")
    from_date: Optional[str] = Field(None, description="Start date of the requested date range")
    to_date: Optional[str] = Field(None, description="End date of the requested date range")
    start_date: Optional[str] = Field(None, description="Actual start date used for normalization (earliest common date)")
    benchmark_indices: Optional[List[str]] = Field(None, description="List of indices used for benchmarking")
    chart_data: Optional[List[BenchmarkDataPoint]] = Field(None, description="Time series data for charting")
    message: Optional[str] = Field(None, description="Error message if success is False")
    warning: Optional[str] = Field(None, description="Warning message if some indices were not available")

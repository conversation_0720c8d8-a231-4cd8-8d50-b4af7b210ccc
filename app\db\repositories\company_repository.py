"""
Company Repository

This module provides repository class for company-related database operations.
"""

import logging
from typing import Dict, List, Optional, Any

from app.db.session import get_database  # Import the database connection function
from app.utils.db_utils import safe_mongodb_operation

# Configure logging
logger = logging.getLogger(__name__)

class CompanyRepository:
    """Repository for company-related database operations."""

    @staticmethod
    async def _process_company_results(companies: List[Dict[str, Any]], context: str = "") -> Dict[str, str]:
        """
        Process company query results into a standardized dictionary format.

        Args:
            companies: List of company documents from MongoDB
            context: Optional context for logging (e.g., "S&PSL20")

        Returns:
            Dictionary mapping company names to ticker symbols
        """
        if not companies:
            logger.warning(f"No {context} companies found in the database")
            return {}

        # First, identify companies with multiple share types
        company_names_count = {}
        for company in companies:
            name = company["COMPANY_NAME"]
            if name in company_names_count:
                company_names_count[name] += 1
            else:
                company_names_count[name] = 1

        # Create a dictionary mapping company names to ticker symbols
        companies_dict = {}
        for company in companies:
            name = company["COMPANY_NAME"]
            ticker = company["TICKER_SYMBOL"]
            share_type = company.get("SHARE_TYPE", "UNKNOWN")

            # If this company has multiple share types, append the share type to the name
            if company_names_count[name] > 1:
                display_name = f"{name} ({share_type})"
            else:
                display_name = name

            companies_dict[display_name] = ticker

        logger.info(f"Retrieved {len(companies_dict)} {context} companies from the database")
        return companies_dict

    @staticmethod
    async def get_sp_sl20_companies() -> Dict[str, str]:
        """
        Get all companies that are constituents of the S&PSL20 index.

        Returns:
            Dictionary mapping company names to ticker symbols.
            For companies with multiple share types (ORDINARY and NON_VOTING),
            the company name will be appended with the share type in parentheses.
        """
        try:
            # Get the database for the current event loop
            db = await get_database()

            # Query MongoDB for companies where S&PSL20 is true
            async def get_companies():
                return await db["companies"].find(
                    {"S&PSL20": True},
                    {
                        "COMPANY_NAME": 1,
                        "TICKER_SYMBOL": 1,
                        "SHARE_TYPE": 1,
                        "_id": 0
                    }
                ).to_list(None)
            companies = await safe_mongodb_operation(get_companies)

            return await CompanyRepository._process_company_results(companies, "S&PSL20")

        except Exception as e:
            logger.error(f"Error retrieving S&PSL20 companies: {str(e)}")
            raise

    @staticmethod
    async def get_all_companies() -> Dict[str, str]:
        """
        Get all companies in the database.

        Returns:
            Dictionary mapping company names to ticker symbols.
            For companies with multiple share types (ORDINARY and NON_VOTING),
            the company name will be appended with the share type in parentheses.
        """
        try:
            # Get the database for the current event loop
            db = await get_database()

            # Query MongoDB for all companies
            async def get_companies():
                return await db["companies"].find(
                    {},
                    {
                        "COMPANY_NAME": 1,
                        "TICKER_SYMBOL": 1,
                        "SHARE_TYPE": 1,
                        "_id": 0
                    }
                ).to_list(None)
            companies = await safe_mongodb_operation(get_companies)

            return await CompanyRepository._process_company_results(companies)

        except Exception as e:
            logger.error(f"Error retrieving companies: {str(e)}")
            raise

    @staticmethod
    async def get_company_sectors(ticker_symbols: Optional[List[str]] = None) -> Dict[str, str]:
        """
        Get the sector for each company in the database or for specific ticker symbols.

        Args:
            ticker_symbols: Optional list of ticker symbols to get sectors for.
                           If None, returns sectors for all companies.

        Returns:
            Dictionary mapping ticker symbols to sectors
        """
        try:
            # Get the database for the current event loop
            db = await get_database()

            # Prepare the query filter
            query_filter = {}
            if ticker_symbols:
                query_filter = {"TICKER_SYMBOL": {"$in": ticker_symbols}}

            # Query MongoDB for companies with sector information
            async def get_companies():
                return await db["companies"].find(
                    query_filter,
                    {
                        "TICKER_SYMBOL": 1,
                        "GICS_SECTOR": 1,
                        "_id": 0
                    }
                ).to_list(None)
            companies = await safe_mongodb_operation(get_companies)

            if not companies:
                if ticker_symbols:
                    logger.warning(f"No sector information found for ticker symbols: {ticker_symbols}")
                else:
                    logger.warning("No companies with sector information found in the database")
                return {}

            # Create a dictionary mapping ticker symbols to sectors
            # companies is already a list from the to_list(None) call above
            companies_list = companies

            sectors_dict = {
                company["TICKER_SYMBOL"]: company.get("GICS_SECTOR", "Unknown")
                for company in companies_list
                if "TICKER_SYMBOL" in company
            }

            logger.info(f"Retrieved sector information for {len(sectors_dict)} companies")
            return sectors_dict

        except Exception as e:
            logger.error(f"Error retrieving company sectors: {str(e)}")
            raise

    @staticmethod
    async def get_all_sectors() -> List[str]:
        """
        Get all unique sectors from the database.

        Returns:
            List of sector names
        """
        try:
            # Get the database for the current event loop
            db = await get_database()

            # Query MongoDB for all unique sectors
            pipeline = [
                {"$group": {"_id": "$GICS_SECTOR"}},
                {"$sort": {"_id": 1}}
            ]

            async def get_sectors():
                return await db["companies"].aggregate(pipeline).to_list(None)
            sectors = await safe_mongodb_operation(get_sectors)

            # Extract sector names and filter out None values
            result = [sector["_id"] for sector in sectors if sector["_id"]]

            logger.info(f"Retrieved {len(result)} unique sectors from the database")
            return result

        except Exception as e:
            logger.error(f"Error retrieving sectors: {str(e)}")
            raise

    @staticmethod
    async def get_industry_groups() -> List[str]:
        """
        Get all unique industry groups from the database.

        Returns:
            List of industry group names
        """
        try:
            # Get the database for the current event loop
            db = await get_database()

            # Query MongoDB for all unique industry groups
            pipeline = [
                {"$group": {"_id": "$GICS_INDUSTRY_GROUP"}},
                {"$sort": {"_id": 1}}
            ]

            async def get_industry_groups():
                return await db["companies"].aggregate(pipeline).to_list(None)
            industry_groups = await safe_mongodb_operation(get_industry_groups)

            # Extract industry group names and filter out None values
            result = [group["_id"] for group in industry_groups if group["_id"]]

            logger.info(f"Retrieved {len(result)} industry groups from the database")
            return result

        except Exception as e:
            logger.error(f"Error retrieving industry groups: {str(e)}")
            raise

    @staticmethod
    async def get_companies_by_industry_groups(industry_groups: List[str]) -> Dict[str, Dict[str, str]]:
        """
        Get companies grouped by the specified industry groups.

        Args:
            industry_groups: List of industry group names to retrieve companies for

        Returns:
            Dictionary mapping industry groups to dictionaries of company names and ticker symbols
        """
        try:
            if not industry_groups:
                return {}

            # Get the database for the current event loop
            db = await get_database()

            # Query MongoDB for companies in the specified industry groups
            async def get_companies():
                return await db["companies"].find(
                    {"GICS_INDUSTRY_GROUP": {"$in": industry_groups}},
                    {
                        "COMPANY_NAME": 1,
                        "TICKER_SYMBOL": 1,
                        "SHARE_TYPE": 1,
                        "GICS_INDUSTRY_GROUP": 1,
                        "_id": 0
                    }
                ).to_list(None)
            companies = await safe_mongodb_operation(get_companies)

            if not companies:
                logger.warning(f"No companies found for industry groups: {industry_groups}")
                return {}

            # First, identify companies with multiple share types
            company_names_count = {}
            for company in companies:
                name = company["COMPANY_NAME"]
                if name in company_names_count:
                    company_names_count[name] += 1
                else:
                    company_names_count[name] = 1

            # Group companies by industry group
            result = {}
            for company in companies:
                name = company["COMPANY_NAME"]
                ticker = company["TICKER_SYMBOL"]
                share_type = company.get("SHARE_TYPE", "UNKNOWN")
                industry_group = company.get("GICS_INDUSTRY_GROUP", "Unknown")

                # If this company has multiple share types, append the share type to the name
                if company_names_count[name] > 1:
                    display_name = f"{name} ({share_type})"
                else:
                    display_name = name

                # Initialize the industry group dictionary if it doesn't exist
                if industry_group not in result:
                    result[industry_group] = {}

                # Add the company to its industry group
                result[industry_group][display_name] = ticker

            logger.info(f"Retrieved companies for {len(result)} industry groups")
            return result

        except Exception as e:
            logger.error(f"Error retrieving companies by industry groups: {str(e)}")
            raise

    @staticmethod
    async def get_companies_by_tickers(tickers: List[str]) -> Dict[str, str]:
        """
        Get company information for specific ticker symbols.

        Args:
            tickers: List of ticker symbols to retrieve

        Returns:
            Dictionary mapping company names to ticker symbols.
            For companies with multiple share types (ORDINARY and NON_VOTING),
            the company name will be appended with the share type in parentheses.
        """
        try:
            if not tickers:
                return {}

            # Get the database for the current event loop
            db = await get_database()

            # Query MongoDB for companies with the specified ticker symbols
            async def get_companies():
                return await db["companies"].find(
                    {"TICKER_SYMBOL": {"$in": tickers}},
                    {
                        "COMPANY_NAME": 1,
                        "TICKER_SYMBOL": 1,
                        "SHARE_TYPE": 1,
                        "_id": 0
                    }
                ).to_list(None)
            companies = await safe_mongodb_operation(get_companies)

            return await CompanyRepository._process_company_results(
                companies,
                f"companies for tickers: {', '.join(tickers[:5])}" + ("..." if len(tickers) > 5 else "")
            )

        except Exception as e:
            logger.error(f"Error retrieving companies by tickers: {str(e)}")
            raise

    @staticmethod
    async def get_company_by_symbol(ticker_symbol: str) -> Optional[Dict[str, Any]]:
        """
        Get company information for a specific ticker symbol.

        Args:
            ticker_symbol: The ticker symbol to retrieve company information for

        Returns:
            Company information dictionary or None if not found
        """
        try:
            # Get the database for the current event loop
            db = await get_database()

            # Query MongoDB for this ticker's company information
            async def get_company():
                return await db["companies"].find_one(
                    {"TICKER_SYMBOL": ticker_symbol},
                    {
                        "COMPANY_NAME": 1,
                        "TICKER_SYMBOL": 1,
                        "SHARE_TYPE": 1,
                        "GICS_SECTOR": 1,
                        "GICS_INDUSTRY_GROUP": 1,
                        "_id": 0
                    }
                )
            company = await safe_mongodb_operation(get_company)

            if not company:
                logger.warning(f"No company information found for {ticker_symbol}")
                return None

            return company

        except Exception as e:
            logger.error(f"Error retrieving company information for {ticker_symbol}: {str(e)}")
            return None

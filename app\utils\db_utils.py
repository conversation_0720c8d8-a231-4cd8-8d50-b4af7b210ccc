"""
Database Utilities

This module provides utility functions for database operations.
"""

import asyncio
import random
import logging
import pymongo.errors
from decimal import Decimal, ROUND_HALF_UP
from bson.decimal128 import Decimal128
from typing import Callable, TypeVar, Optional, Any, Union

# Configure logging
logger = logging.getLogger(__name__)

# Type variable for generic function return type
T = TypeVar('T')

async def safe_mongodb_operation(operation: Callable[[], T], fallback_value: Optional[T] = None, max_retries: int = 3) -> T:
    """
    Execute a MongoDB operation with retry logic.

    Args:
        operation: Async function that performs a MongoDB operation
        fallback_value: Value to return if all retries fail
        max_retries: Maximum number of retry attempts

    Returns:
        Result of the operation or fallback_value if all retries fail
    """
    from app.db.session import get_database, release_database

    retries = 0
    db = None

    while retries <= max_retries:
        try:
            # Get the current event loop or create a new one
            try:
                asyncio.get_running_loop()
                # If we get here, we have a running event loop
            except RuntimeError:
                # No running event loop, create a new one
                # This should rarely happen, but we handle it just in case
                logger.debug("No running event loop found, creating a new one for MongoDB operation")
                loop = asyncio.new_event_loop()
                asyncio.set_event_loop(loop)

            # Get a database connection with reference counting
            db = await get_database()

            # Call the operation function directly (it should return a cursor or other MongoDB object)
            result = operation()

            # If the result is a cursor, we need to handle it differently
            if hasattr(result, 'to_list'):
                # For cursors, we need to call to_list directly
                result_value = await result.to_list(length=None)
                # Release the database connection
                await release_database()
                return result_value

            # For other async operations (like find_one), await the result
            result_value = await result
            # Release the database connection
            await release_database()
            return result_value

        except pymongo.errors.PyMongoError as e:
            retries += 1
            if retries <= max_retries:
                # Exponential backoff
                delay = 1.0 * (2 ** (retries - 1)) + (random.random() * 0.1)
                logger.warning(f"MongoDB operation failed, retrying in {delay:.2f}s ({retries}/{max_retries}): {str(e)}")
                await asyncio.sleep(delay)
            else:
                logger.error(f"MongoDB operation failed after {max_retries} retries: {str(e)}")
                # Release the database connection if we got one
                if db is not None:
                    await release_database()
                return fallback_value
        except asyncio.InvalidStateError as e:
            logger.error(f"AsyncIO state error in MongoDB operation: {str(e)}")
            # This typically happens when trying to await a coroutine from a different event loop
            # Release the database connection if we got one
            if db is not None:
                await release_database()
            # Return the fallback value instead of raising
            return fallback_value
        except RuntimeError as e:
            error_msg = str(e).lower()
            if "different loop" in error_msg or "attached to a different loop" in error_msg or "event loop is closed" in error_msg:
                logger.error(f"Event loop error in MongoDB operation: {str(e)}")
                # This is a common issue with scheduler jobs
                # Release the database connection if we got one
                if db is not None:
                    await release_database()
                return fallback_value
            logger.error(f"Runtime error in MongoDB operation: {str(e)}")
            # Release the database connection if we got one
            if db is not None:
                await release_database()
            raise
        except Exception as e:
            logger.error(f"Unexpected error in MongoDB operation: {str(e)}")
            # Release the database connection if we got one
            if db is not None:
                await release_database()
            raise

    # Release the database connection if we got one
    if db is not None:
        await release_database()
    return fallback_value


def safe_decimal128(value: Union[float, int, str, Decimal, None], context_name: str = "unknown") -> Decimal128:
    """
    Safely convert a value to Decimal128 for MongoDB storage.

    Args:
        value: The value to convert
        context_name: A name for the context (for logging purposes)

    Returns:
        Decimal128 representation of the value
    """
    try:
        if value is None:
            return Decimal128('0')

        if isinstance(value, (float, int)):
            # Convert to Decimal first for proper rounding
            decimal_value = Decimal(str(value)).quantize(Decimal('0.01'), rounding=ROUND_HALF_UP)
            return Decimal128(str(decimal_value))

        if isinstance(value, str):
            # Parse string to Decimal
            decimal_value = Decimal(value).quantize(Decimal('0.01'), rounding=ROUND_HALF_UP)
            return Decimal128(str(decimal_value))

        if isinstance(value, Decimal):
            # Already a Decimal, just quantize
            decimal_value = value.quantize(Decimal('0.01'), rounding=ROUND_HALF_UP)
            return Decimal128(str(decimal_value))

        if isinstance(value, Decimal128):
            # Already a Decimal128, return as is
            return value

        # If we get here, try a generic conversion
        return Decimal128(str(Decimal(str(value)).quantize(Decimal('0.01'), rounding=ROUND_HALF_UP)))

    except Exception as e:
        logger.error(f"Error converting value to Decimal128 in context '{context_name}': {str(e)}")
        # Return 0 as a safe fallback
        return Decimal128('0')
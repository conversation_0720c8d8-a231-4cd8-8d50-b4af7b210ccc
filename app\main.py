"""
Main module for the CSE Portfolio Builder API

This is the entry point for the FastAPI application that serves the CSE Portfolio Builder API.
"""

import logging
from contextlib import asynccontextmanager
import uvicorn
from fastapi import FastAPI, Depends
from fastapi.middleware.cors import CORSMiddleware
from fastapi.security import HTTPBearer
from app.middleware import ClerkMiddleware

# Import from new structure
from app.controllers.portfolio_controller import router as portfolio_router
from app.controllers.company_controller import router as company_router
from app.controllers.trade_controller import router as trade_router
from app.controllers.user_controller import router as user_router
from app.controllers.news_controller import router as news_router
from app.controllers.clerk_webhook import webhook_router

# Scheduler services import
from app.services.scheduler_service import (fetch_daily_trades_scheduler,
                                        setup_db_cleanup_scheduler)

# Configure logging
logging.basicConfig(
    level=logging.INFO,  # Set to DEBUG for more detailed logs
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),  # Log to console
        logging.FileHandler('app.log')  # Log to file
    ]
)

# Set specific loggers to DEBUG
logging.getLogger('app.controllers.clerk_webhook').setLevel(logging.INFO)
logging.getLogger('app.middleware').setLevel(logging.INFO)

bearer_scheme = HTTPBearer(scheme_name="Bearer")

@asynccontextmanager
async def lifespan(_: FastAPI):
    """
    Lifespan context manager for the FastAPI application.

    This context manager is used to start the schedulers on app startup
    and properly shut down database connections on app shutdown.
    """
    # Start the sequential job scheduler on app startup
    # This sets up a chain of jobs where each job triggers the next one after completion:
    # 1. fetch_daily_trades -> 2. update_portfolios -> 3. check_portfolio_rebalancing -> 4. fetch_index_data
    fetch_daily_trades_scheduler()

    # Disable the database cleanup scheduler as it's causing event loop issues
    # setup_db_cleanup_scheduler()

    # Yield control back to the application
    yield

    # On shutdown, close all database connections
    from app.db.session import shutdown_db
    await shutdown_db()

# Create FastAPI application
app = FastAPI(
    title="CSE Portfolio Builder API",
    description="""
    API for building and managing optimized equity portfolios using Colombo Stock Exchange (CSE) data.

    ## Portfolio Management

    The API provides endpoints for:

    * Creating optimized portfolios (Minimum Variance, Maximum Sharpe Ratio, or Custom)
    * Retrieving portfolio details and performance metrics
    * Updating portfolios with latest market data
    * Deleting portfolios

    ## Key Features

    * **Portfolio Optimization**: Uses modern portfolio theory with Ledoit-Wolf shrinkage and winsorization
    * **Real-time Data**: Integrates with CSE data sources for up-to-date market information
    * **Risk Management**: Provides volatility metrics and optimized allocations
    * **Daily Updates**: Automatically updates portfolio values with latest market data

    ## Authentication

    Most endpoints require authentication. Use the frontend application to authenticate.
    """,
    version="0.2.0",
    lifespan=lifespan,
)

# Configure CORS for frontend communication
app.add_middleware(
    CORSMiddleware,
    allow_origins=["http://localhost:3000",
                "https://d8fa-2402-d000-8114-131-3d81-547c-b6e6-9bb4.ngrok-free.app"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Add Clerk authentication middleware for API endpoints
app.add_middleware(
    ClerkMiddleware,
    protected_paths=["/api/*"],
    enforce_authentication=True  # Automatically return 401 for unauthenticated requests
)

# Include all routers
app.include_router(webhook_router)
app.include_router(portfolio_router, dependencies=[Depends(bearer_scheme)])
app.include_router(company_router, dependencies=[Depends(bearer_scheme)])
app.include_router(user_router, dependencies=[Depends(bearer_scheme)])
app.include_router(trade_router, dependencies=[Depends(bearer_scheme)])
app.include_router(news_router, dependencies=[Depends(bearer_scheme)])


@app.get("/")
async def root() -> dict:
    """Root endpoint with API information."""
    return {
        "message": "CSE Portfolio Builder API",
        "description": "API for building portfolios using CSE stock data",
        "version": "0.2.0",
        "documentation": "/docs",
        "redoc": "/redoc"
    }

if __name__ == "__main__":
    uvicorn.run("app.main:app", port=8000, reload=True)

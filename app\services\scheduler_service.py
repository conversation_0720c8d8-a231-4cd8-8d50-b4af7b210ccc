"""
Scheduler Service

This module provides scheduling functionality for recurring tasks.
"""

import logging
import asyncio
from datetime import datetime
from apscheduler.schedulers.asyncio import AsyncIOScheduler
from apscheduler.triggers.cron import CronTrigger
from apscheduler.triggers.interval import IntervalTrigger
from app.services.cse_trade_summary_service import fetch_and_store_trade_summary
from app.services.portfolio_update_service import update_all_portfolios
from app.services.portfolio_rebalance_service import check_portfolios_for_rebalancing
from app.services.index_data_service import fetch_and_store_index_data
from app.utils.date_utils import skip_if_weekend

logger = logging.getLogger(__name__)
scheduler = AsyncIOScheduler()

async def scheduled_fetch_daily_trades():
    """Wrapper function for the scheduled job to fetch daily trades.

    This function logs the start and completion of the scheduled job and handles any exceptions.
    It also handles the case where the API returns data for the last trading day.
    After completion, it triggers the portfolio update job.
    """
    logger.info("Scheduled job: Starting daily trade data fetch")
    try:
        result = await fetch_and_store_trade_summary(force_fetch=False)

        if result.get("error"):
            logger.error("Scheduled job: %s", result.get('message'))
        elif result.get("trade_date"):
            # We have a trade date in the response
            if result.get("records_inserted", 0) > 0:
                logger.info("Scheduled job: Inserted %d records for trade date %s",
                            result.get('records_inserted'),
                            result.get('trade_date'))
            else:
                logger.info("Scheduled job: %s for trade date %s",
                            result.get('message'),
                            result.get('trade_date'))
        else:
            # Generic case
            logger.info("Scheduled job: %s", result.get('message'))
    except ConnectionError as e:
        logger.error("Scheduled job: Connection error in daily trade fetch: %s", str(e))
    except TimeoutError as e:
        logger.error("Scheduled job: Timeout error in daily trade fetch: %s", str(e))
    except ValueError as e:
        logger.error("Scheduled job: Value error in daily trade fetch: %s", str(e))
    finally:
        logger.info("Scheduled job: Completed daily trade data fetch")

        # Trigger the next job in the sequence
        logger.info("Triggering portfolio update job...")
        # Use run_in_executor to avoid blocking the current event loop
        loop = asyncio.get_event_loop()
        loop.run_in_executor(None, run_portfolio_update_job)

def run_async_job():
    """
    Helper function to run the async scheduled job in the event loop.
    This is needed because APScheduler can't directly schedule coroutines.

    This function creates a dedicated event loop for the scheduled job to avoid
    conflicts with the main FastAPI application event loop.
    """
    # Create a new event loop for this job
    job_loop = asyncio.new_event_loop()

    try:
        # Set the event loop for this thread
        asyncio.set_event_loop(job_loop)

        # Run the coroutine in the new event loop
        logger.debug("Running scheduled job in dedicated event loop")
        job_loop.run_until_complete(scheduled_fetch_daily_trades())
    except asyncio.CancelledError:
        logger.warning("Async job was cancelled")
    except asyncio.TimeoutError:
        logger.error("Async job timed out")
    except asyncio.InvalidStateError as e:
        logger.error("Invalid state in async job: %s", str(e))
    except Exception as e:
        logger.error("Error in scheduled job: %s", str(e))
    finally:
        # Properly clean up the event loop
        try:
            # Shutdown async generators
            job_loop.run_until_complete(job_loop.shutdown_asyncgens())
            # Close the event loop
            job_loop.close()
            logger.debug("Closed dedicated event loop for scheduled job")
        except Exception as e:
            logger.error(f"Error closing event loop: {str(e)}")

def fetch_daily_trades_scheduler():
    """Set up the scheduler for the sequential job chain.

    This function configures the scheduler to run the initial job (daily trade fetch) at 2:31 PM
    (1 minute after CSE market close) every day. This job will then trigger the next job in the sequence,
    and so on, ensuring that each job runs only after the previous one has completed.

    The job will automatically handle weekends and non-trading days.

    Note: In production, it's recommended to run the scheduler in a separate process
    to avoid event loop conflicts with the main FastAPI application.
    """
    # Daily at 2:31 PM (1 minute after CSE market close at 2:30 PM)
    trigger = CronTrigger(hour=23, minute=58, second=00, timezone="Asia/Colombo")

    # Schedule only the first job in the chain - it will trigger the others
    scheduler.add_job(run_async_job, trigger, id="daily_trade_fetch",
                    replace_existing=True,  # Replace if job already exists
                    misfire_grace_time=3600)  # Allow job to run up to 1 hour late

    logger.info("Scheduler: Sequential job chain scheduled to start at 2:31 PM (1 minute after market close)")

    # Start the scheduler if it's not already running
    if not scheduler.running:
        scheduler.start()

async def scheduled_update_portfolios():
    """Wrapper function for the scheduled job to update portfolios.

    This function logs the start and completion of the scheduled job and handles any exceptions.
    It updates all active portfolios with the latest market data.
    After completion, it triggers the portfolio rebalance job.
    """
    logger.info("Scheduled job: Starting daily portfolio update")
    try:
        result = await update_all_portfolios()

        if result.get("success"):
            logger.info(f"Scheduled job: Successfully updated {result.get('portfolios_updated')} portfolios")
        else:
            logger.error(f"Scheduled job: Portfolio update failed - {result.get('message')}")
    except Exception as e:
        logger.error(f"Scheduled job: Error in portfolio update: {str(e)}")
    finally:
        logger.info("Scheduled job: Completed daily portfolio update")

        # Trigger the next job in the sequence
        logger.info("Triggering portfolio rebalance job...")
        # Use run_in_executor to avoid blocking the current event loop
        loop = asyncio.get_event_loop()
        loop.run_in_executor(None, run_portfolio_rebalance_job)

def run_portfolio_update_job():
    """
    Helper function to run the async portfolio update job in the event loop.
    This is needed because APScheduler can't directly schedule coroutines.

    This function creates a dedicated event loop for the scheduled job to avoid
    conflicts with the main FastAPI application event loop.
    """
    # Create a new event loop for this job
    job_loop = asyncio.new_event_loop()

    try:
        # Set the event loop for this thread
        asyncio.set_event_loop(job_loop)

        # Run the coroutine in the new event loop
        logger.debug("Running portfolio update job in dedicated event loop")
        job_loop.run_until_complete(scheduled_update_portfolios())
    except asyncio.CancelledError:
        logger.warning("Portfolio update job was cancelled")
    except asyncio.TimeoutError:
        logger.error("Portfolio update job timed out")
    except asyncio.InvalidStateError as e:
        logger.error(f"Invalid state in portfolio update job: {str(e)}")
    except Exception as e:
        logger.error(f"Error in portfolio update job: {str(e)}")
    finally:
        # Properly clean up the event loop
        try:
            # Shutdown async generators
            job_loop.run_until_complete(job_loop.shutdown_asyncgens())
            # Close the event loop
            job_loop.close()
            logger.debug("Closed dedicated event loop for portfolio update job")
        except Exception as e:
            logger.error(f"Error closing event loop: {str(e)}")




def run_db_cleanup_job():
    """
    Helper function to run the database connection cleanup job.

    This function cleans up any MongoDB clients associated with closed event loops
    to prevent connection leaks over time.
    """
    # Create a new event loop for this job
    job_loop = asyncio.new_event_loop()

    try:
        # Set the event loop for this thread
        asyncio.set_event_loop(job_loop)

        # Run the coroutine in the new event loop
        logger.debug("Running database connection cleanup job in dedicated event loop")
        job_loop.run_until_complete(run_async_db_cleanup())
    except Exception as e:
        logger.error(f"Error in database connection cleanup job: {str(e)}")
    finally:
        # Properly clean up the event loop
        try:
            # Shutdown async generators
            job_loop.run_until_complete(job_loop.shutdown_asyncgens())
            # Close the event loop
            job_loop.close()
            logger.debug("Closed dedicated event loop for database cleanup job")
        except Exception as e:
            logger.error(f"Error closing event loop: {str(e)}")

async def run_async_db_cleanup():
    """
    Async function to run the database connection cleanup.
    """
    try:
        logger.debug("Running async database connection cleanup")
        from app.db.session import cleanup_closed_loops
        await cleanup_closed_loops()
        logger.debug("Async database connection cleanup completed")
    except Exception as e:
        logger.error(f"Error in async database connection cleanup: {str(e)}")


def setup_db_cleanup_scheduler():
    """Set up the scheduler for periodic database connection cleanup.

    This function configures the scheduler to run every hour to clean up
    any MongoDB clients associated with closed event loops.
    """
    # Run every hour
    trigger = IntervalTrigger(hours=1)

    # Schedule the cleanup job
    scheduler.add_job(run_db_cleanup_job, trigger, id="db_connection_cleanup",
                     replace_existing=True,  # Replace if job already exists
                     misfire_grace_time=3600)  # Allow job to run up to 1 hour late

    logger.info("Scheduler: Database connection cleanup job scheduled to run hourly")


async def scheduled_check_portfolio_rebalancing():
    """Wrapper function for the scheduled job to check portfolios for rebalancing.

    This function logs the start and completion of the scheduled job and handles any exceptions.
    It checks all active portfolios to see if any need rebalancing based on their rebalance frequency.
    After completion, it triggers the index data fetch job.
    """
    logger.info("Scheduled job: Starting portfolio rebalance check")
    try:
        result = await check_portfolios_for_rebalancing()

        if result.get("success"):
            rebalanced = result.get('portfolios_rebalanced', 0)
            checked = result.get('portfolios_checked', 0)
            failed = result.get('portfolios_failed', 0)
            logger.info(f"Scheduled job: Checked {checked} portfolios, rebalanced {rebalanced}, failed {failed}")
        else:
            logger.error(f"Scheduled job: Portfolio rebalance check failed - {result.get('message')}")
    except Exception as e:
        logger.error(f"Scheduled job: Error in portfolio rebalance check: {str(e)}")
    finally:
        logger.info("Scheduled job: Completed portfolio rebalance check")

        # Trigger the next job in the sequence
        logger.info("Triggering index data fetch job...")
        # Use run_in_executor to avoid blocking the current event loop
        loop = asyncio.get_event_loop()
        loop.run_in_executor(None, run_index_data_job)


def run_portfolio_rebalance_job():
    """
    Helper function to run the async portfolio rebalance check job in the event loop.
    This is needed because APScheduler can't directly schedule coroutines.

    This function creates a dedicated event loop for the scheduled job to avoid
    conflicts with the main FastAPI application event loop.
    """
    # Create a new event loop for this job
    job_loop = asyncio.new_event_loop()

    try:
        # Set the event loop for this thread
        asyncio.set_event_loop(job_loop)

        # Run the coroutine in the new event loop
        logger.debug("Running portfolio rebalance check job in dedicated event loop")
        job_loop.run_until_complete(scheduled_check_portfolio_rebalancing())
    except asyncio.CancelledError:
        logger.warning("Portfolio rebalance check job was cancelled")
    except asyncio.TimeoutError:
        logger.error("Portfolio rebalance check job timed out")
    except asyncio.InvalidStateError as e:
        logger.error(f"Invalid state in portfolio rebalance check job: {str(e)}")
    except Exception as e:
        logger.error(f"Error in portfolio rebalance check job: {str(e)}")
    finally:
        # Properly clean up the event loop
        try:
            # Shutdown async generators
            job_loop.run_until_complete(job_loop.shutdown_asyncgens())
            # Close the event loop
            job_loop.close()
            logger.debug("Closed dedicated event loop for portfolio rebalance check job")
        except Exception as e:
            logger.error(f"Error closing event loop: {str(e)}")




async def scheduled_fetch_index_data():
    """Wrapper function for the scheduled job to fetch market index data.

    This function logs the start and completion of the scheduled job and handles any exceptions.
    It fetches and stores ASPI and S&PSL20 index data.
    """
    logger.info("Scheduled job: Starting market index data fetch")
    try:
        result = await fetch_and_store_index_data()

        if result.get("success"):
            logger.info("Scheduled job: Successfully fetched and stored market index data")
        else:
            logger.error(f"Scheduled job: Market index data fetch failed - {result.get('message')}")
    except Exception as e:
        logger.error(f"Scheduled job: Error in market index data fetch: {str(e)}")

    logger.info("Scheduled job: Completed market index data fetch")

def run_index_data_job():
    """
    Helper function to run the async index data fetch job in the event loop.
    This is needed because APScheduler can't directly schedule coroutines.

    This function creates a dedicated event loop for the scheduled job to avoid
    conflicts with the main FastAPI application event loop.
    """
    # Create a new event loop for this job
    job_loop = asyncio.new_event_loop()

    try:
        # Set the event loop for this thread
        asyncio.set_event_loop(job_loop)

        # Run the coroutine in the new event loop
        logger.debug("Running index data fetch job in dedicated event loop")
        job_loop.run_until_complete(scheduled_fetch_index_data())
    except asyncio.CancelledError:
        logger.warning("Index data fetch job was cancelled")
    except asyncio.TimeoutError:
        logger.error("Index data fetch job timed out")
    except asyncio.InvalidStateError as e:
        logger.error(f"Invalid state in index data fetch job: {str(e)}")
    except Exception as e:
        logger.error(f"Error in index data fetch job: {str(e)}")
    finally:
        # Properly clean up the event loop
        try:
            # Shutdown async generators
            job_loop.run_until_complete(job_loop.shutdown_asyncgens())
            # Close the event loop
            job_loop.close()
            logger.debug("Closed dedicated event loop for index data fetch job")
        except Exception as e:
            logger.error(f"Error closing event loop: {str(e)}")




async def manual_fetch_daily_trades(force: bool = False):
    """
    Manually run the daily trade fetch job.

    This function is useful for testing the scheduler functionality without waiting for the scheduled time.
    It runs the scheduled job directly in the current event loop, which is safer for manual testing.

    Args:
        force: If True, fetch data even if it already exists or it's a weekend

    Returns:
        Result of the fetch operation
    """
    logger.info(f"Manual trigger: Running daily trade fetch job (force={force})")
    try:
        # Call the fetch function directly with the force parameter
        result = await fetch_and_store_trade_summary(force_fetch=force)
        return result
    except Exception as e:
        error_msg = f"Error executing manual trade fetch: {str(e)}"
        logger.error(error_msg)
        return {"error": True, "message": error_msg}

async def manual_fetch_index_data(force: bool = False):
    """
    Manually run the market index data fetch job.

    This function is useful for testing the scheduler functionality without waiting for the scheduled time.
    It runs the scheduled job directly in the current event loop, which is safer for manual testing.

    Args:
        force: If True, fetch data even on weekends

    Returns:
        Result of the fetch operation
    """
    logger.info(f"Manual trigger: Running market index data fetch job (force={force})")
    try:
        # Check if today is a weekend
        current_date = datetime.now()
        weekend_skip = skip_if_weekend(current_date, "index data fetch", force=force)
        if weekend_skip:
            # Add service-specific fields to the standard weekend skip response
            weekend_skip.update({
                "aspi": {"success": True, "message": "Skipped (weekend)"},
                "snpsl20": {"success": True, "message": "Skipped (weekend)"}
            })
            return weekend_skip

        # Call the fetch function directly
        result = await fetch_and_store_index_data()
        return result
    except Exception as e:
        error_msg = f"Error executing manual index data fetch: {str(e)}"
        logger.error(error_msg)
        return {"error": True, "message": error_msg}

async def manual_update_portfolios():
    """
    Manually run the portfolio update job.

    This function is useful for testing the scheduler functionality without waiting for the scheduled time.
    It runs the scheduled job directly in the current event loop, which is safer for manual testing.

    Returns:
        Result of the update operation
    """
    logger.info("Manual trigger: Running portfolio update job")
    try:
        result = await update_all_portfolios()
        return result
    except Exception as e:
        error_msg = f"Error executing manual portfolio update: {str(e)}"
        logger.error(error_msg)
        return {"error": True, "message": error_msg}

async def manual_check_portfolio_rebalancing():
    """
    Manually run the portfolio rebalancing check job.

    This function is useful for testing the scheduler functionality without waiting for the scheduled time.
    It runs the scheduled job directly in the current event loop, which is safer for manual testing.

    Returns:
        Result of the rebalancing check operation
    """
    logger.info("Manual trigger: Running portfolio rebalancing check job")
    try:
        result = await check_portfolios_for_rebalancing()
        return result
    except Exception as e:
        error_msg = f"Error executing manual portfolio rebalancing check: {str(e)}"
        logger.error(error_msg)
        return {"error": True, "message": error_msg}

async def manual_db_cleanup():
    """
    Manually run the database cleanup job.

    This function is useful for testing the scheduler functionality without waiting for the scheduled time.
    It runs the scheduled job directly in the current event loop, which is safer for manual testing.

    Returns:
        Result of the cleanup operation
    """
    logger.info("Manual trigger: Running database cleanup job")
    try:
        await run_async_db_cleanup()
        return {"success": True, "message": "Database cleanup completed successfully"}
    except Exception as e:
        error_msg = f"Error executing manual database cleanup: {str(e)}"
        logger.error(error_msg)
        return {"error": True, "message": error_msg}

async def run_all_scheduler_jobs(force: bool = False):
    """
    Manually run all scheduler jobs in sequence.

    This function runs all the scheduled jobs in the same order they would normally execute:
    1. Daily trade fetch
    2. Portfolio update
    3. Portfolio rebalancing check
    4. Index data fetch
    5. Database cleanup

    Args:
        force: If True, run jobs even on weekends and force data fetch

    Returns:
        Dictionary containing results of all job executions
    """
    logger.info(f"Manual trigger: Running all scheduler jobs in sequence (force={force})")

    results = {
        "overall_success": True,
        "jobs": {}
    }

    # Job 1: Daily trade fetch
    logger.info("Step 1/5: Running daily trade fetch...")
    try:
        trade_result = await manual_fetch_daily_trades(force=force)
        results["jobs"]["daily_trade_fetch"] = trade_result
        if trade_result.get("error"):
            results["overall_success"] = False
            logger.warning("Daily trade fetch failed, but continuing with other jobs")
    except Exception as e:
        error_msg = f"Failed to run daily trade fetch: {str(e)}"
        logger.error(error_msg)
        results["jobs"]["daily_trade_fetch"] = {"error": True, "message": error_msg}
        results["overall_success"] = False

    # Job 2: Portfolio update
    logger.info("Step 2/5: Running portfolio update...")
    try:
        portfolio_result = await manual_update_portfolios()
        results["jobs"]["portfolio_update"] = portfolio_result
        if not portfolio_result.get("success"):
            results["overall_success"] = False
            logger.warning("Portfolio update failed, but continuing with other jobs")
    except Exception as e:
        error_msg = f"Failed to run portfolio update: {str(e)}"
        logger.error(error_msg)
        results["jobs"]["portfolio_update"] = {"error": True, "message": error_msg}
        results["overall_success"] = False

    # Job 3: Portfolio rebalancing check
    logger.info("Step 3/5: Running portfolio rebalancing check...")
    try:
        rebalance_result = await manual_check_portfolio_rebalancing()
        results["jobs"]["portfolio_rebalancing"] = rebalance_result
        if not rebalance_result.get("success"):
            results["overall_success"] = False
            logger.warning("Portfolio rebalancing check failed, but continuing with other jobs")
    except Exception as e:
        error_msg = f"Failed to run portfolio rebalancing check: {str(e)}"
        logger.error(error_msg)
        results["jobs"]["portfolio_rebalancing"] = {"error": True, "message": error_msg}
        results["overall_success"] = False

    # Job 4: Index data fetch
    logger.info("Step 4/5: Running index data fetch...")
    try:
        index_result = await manual_fetch_index_data(force=force)
        results["jobs"]["index_data_fetch"] = index_result
        if index_result.get("error"):
            results["overall_success"] = False
            logger.warning("Index data fetch failed, but continuing with other jobs")
    except Exception as e:
        error_msg = f"Failed to run index data fetch: {str(e)}"
        logger.error(error_msg)
        results["jobs"]["index_data_fetch"] = {"error": True, "message": error_msg}
        results["overall_success"] = False

    # Job 5: Database cleanup
    logger.info("Step 5/5: Running database cleanup...")
    try:
        cleanup_result = await manual_db_cleanup()
        results["jobs"]["database_cleanup"] = cleanup_result
        if cleanup_result.get("error"):
            results["overall_success"] = False
            logger.warning("Database cleanup failed")
    except Exception as e:
        error_msg = f"Failed to run database cleanup: {str(e)}"
        logger.error(error_msg)
        results["jobs"]["database_cleanup"] = {"error": True, "message": error_msg}
        results["overall_success"] = False

    if results["overall_success"]:
        logger.info("Manual trigger: All scheduler jobs completed successfully")
    else:
        logger.warning("Manual trigger: Some scheduler jobs failed - check individual job results")

    return results

'''
Trade models
'''

import pydantic

class Trade(pydantic.BaseModel):
    '''
    Trade model
    '''
    tradeDate: str
    symbol: str
    tradeVolume: int
    open: float
    low: float
    close: float
    shareVolume: int
    securityId: int
    high: float
    id: int
    turnover: float

class TradesResponse(pydantic.BaseModel):
    '''
    Trades response model
    '''
    data: list[Trade]
    total: int
    page: int
    limit: int
    totalPages: int
    hasNext: bool
    hasPrevious: bool

class MostActiveTrade(pydantic.BaseModel):
    '''
    Most active trade model
    '''
    id: int
    securityId: int
    symbol: str
    tradeVolume: float
    shareVolume: float
    turnover: float
    percentageShareVolume: float

class TopGainersLosers(pydantic.BaseModel):
    '''
    Top gainers losers model
    '''
    id: int
    securityId: int
    symbol: str
    price: float
    change: float
    changePercentage: float
    tradeDate: int

class TopGainersLosersResponse(pydantic.BaseModel):
    '''
    Top gainers losers response model
    '''
    gainers: list[TopGainersLosers]
    losers: list[TopGainersLosers]

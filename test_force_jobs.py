#!/usr/bin/env python3
"""
Test script to call the run-all-jobs endpoint with force=true
"""

import requests
import json

def test_force_jobs():
    """Test the force run all jobs endpoint"""
    url = "http://127.0.0.1:8000/api/trades/run-all-jobs"
    params = {"force": True}
    
    print("Testing force run all scheduler jobs...")
    print(f"URL: {url}")
    print(f"Parameters: {params}")
    print("-" * 50)
    
    try:
        response = requests.post(url, params=params, timeout=300)  # 5 minute timeout
        
        print(f"Status Code: {response.status_code}")
        print(f"Response Headers: {dict(response.headers)}")
        print("-" * 50)
        
        if response.status_code == 200:
            result = response.json()
            print("SUCCESS! Response:")
            print(json.dumps(result, indent=2))
        else:
            print(f"ERROR! Status: {response.status_code}")
            print(f"Response: {response.text}")
            
    except requests.exceptions.Timeout:
        print("ERROR: Request timed out after 5 minutes")
    except requests.exceptions.ConnectionError:
        print("ERROR: Could not connect to the server")
    except Exception as e:
        print(f"ERROR: {str(e)}")

if __name__ == "__main__":
    test_force_jobs()

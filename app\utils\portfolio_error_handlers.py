"""
Portfolio Error Handlers

This module provides specialized error handlers for portfolio-related operations.
"""

import logging
from fastapi import HTTPException, status

from app.models.error_models import (
    ErrorTypes,
    create_error_response
)

# Configure logging
logger = logging.getLogger(__name__)

def handle_portfolio_creation_error(error: Exception) -> HTTPException:
    """
    Handle errors specific to portfolio creation operations.

    Args:
        error: The exception that was raised during portfolio creation

    Returns:
        HTTPException with appropriate status code and error details
    """
    # Check if this is an APIError first
    from app.utils.error_handlers import APIError, handle_error
    if isinstance(error, APIError):
        # Special handling for target return errors
        if error.error_code == "INEFFICIENT_TARGET_RETURN":
            return HTTPException(
                status_code=error.status_code,
                detail={
                    "error_code": "INEFFICIENT_TARGET_RETURN",
                    "detail": "Inefficient Target Return!",
                    "explanation": "Your target return is below the lowest optimal target return for the specified companies and constraints",
                    "fix": error.recommendation
                }
            )
        elif error.error_code == "UNACHIEVABLE_TARGET_RETURN":
            return HTTPException(
                status_code=error.status_code,
                detail={
                    "error_code": "UNACHIEVABLE_TARGET_RETURN",
                    "detail": "Target Return Unachievable!",
                    "explanation": "Your target return is above the maximum optimal target return for the specified companies and constraints",
                    "fix": error.recommendation
                }
            )
        elif error.error_code == "NO_COMMON_TRADING_DAYS":
            # Handle the trade data error with the new format
            error_message = error.detail
            explanation = "Not enough overlapping trade data found for the selected companies"

            # Extract company name and ticker from the error message if it's a specific "No trade data found" error
            if "No trade data found for" in error_message:
                import re
                match = re.search(r"No trade data found for (.+?) \((.+?)\) in the specified date range", error_message)
                if match:
                    company_name = match.group(1)
                    ticker = match.group(2)
                    explanation = f"No trade data found for {company_name} ({ticker}) in the specified date range"

            return HTTPException(
                status_code=status.HTTP_422_UNPROCESSABLE_ENTITY,
                detail={
                    "error_code": "NOT_ENOUGH_TRADE_DATA",
                    "detail": "Not enough overlapping trade data found for the selected companies",
                    "explanation": explanation,
                    "fix": "Change the selected companies or the date range"
                }
            )
        # For other API errors, use the standard handler
        return handle_error(error)

    # For non-APIError exceptions, continue with the existing logic
    error_message = str(error)

    # Handle missing trade data error and missing common trading days error (both are the same issue)
    if "No trade data found for" in error_message or "No common trading days" in error_message:
        # Extract company name and ticker from the error message if it's a specific "No trade data found" error
        explanation = "Not enough overlapping trade data found for the selected companies"
        if "No trade data found for" in error_message:
            import re
            match = re.search(r"No trade data found for (.+?) \((.+?)\) in the specified date range", error_message)
            if match:
                company_name = match.group(1)
                ticker = match.group(2)
                explanation = f"No trade data found for {company_name} ({ticker}) in the specified date range"

        return HTTPException(
            status_code=status.HTTP_422_UNPROCESSABLE_ENTITY,
            detail={
                "error_code": "NOT_ENOUGH_TRADE_DATA",
                "detail": "Not enough overlapping trade data found for the selected companies",
                "explanation": explanation,
                "fix": "Change the selected companies or the date range"
            }
        )

    # Handle missing companies error
    if "No companies selected" in error_message:
        return HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=create_error_response(
                issue=ErrorTypes.MISSING_COMPANIES,
                description="No companies were selected for the custom portfolio.",
                fix="Please select at least 5 companies for your portfolio.",
                status_code=status.HTTP_400_BAD_REQUEST
            )
        )

    # Handle insufficient companies error
    if "At least 5 companies" in error_message:
        return HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=create_error_response(
                issue=ErrorTypes.INSUFFICIENT_COMPANIES,
                description="Not enough companies selected for optimal diversification.",
                fix="Select at least 5 different companies to create a well-diversified portfolio.",
                status_code=status.HTTP_400_BAD_REQUEST
            )
        )

    # Handle invalid portfolio type error
    if "Invalid portfolio type" in error_message:
        return HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=create_error_response(
                issue=ErrorTypes.INVALID_PORTFOLIO_TYPE,
                description="The portfolio type is not supported.",
                fix="Please select one of the supported portfolio types: Minimum Variance, Maximum Sharpe, or Custom.",
                status_code=status.HTTP_400_BAD_REQUEST
            )
        )

    # Handle target return validation errors
    if "Target return" in error_message:
        if "below the minimum variance" in error_message:
            # Extract the minimum variance return value from the error message
            import re
            min_var_match = re.search(r"minimum variance portfolio return of (\d+\.\d+)%", error_message)
            min_var_return = min_var_match.group(1) if min_var_match else None

            # Format according to the specified error response
            return HTTPException(
                status_code=status.HTTP_422_UNPROCESSABLE_ENTITY,
                detail={
                    "error_code": "INEFFICIENT_TARGET_RETURN",
                    "detail": "Inefficient Target Return!",
                    "explanation": "Your target return is below the lowest optimal target return for the specified companies and constraints",
                    "fix": f"Increase your target return to {min_var_return}% or above"
                }
            )
        elif "above the maximum Sharpe ratio" in error_message:
            # Extract the maximum Sharpe ratio return value from the error message
            import re
            max_sharpe_match = re.search(r"maximum Sharpe ratio portfolio return of (\d+\.\d+)%", error_message)
            max_sharpe_return = max_sharpe_match.group(1) if max_sharpe_match else None

            # Format according to the specified error response
            return HTTPException(
                status_code=status.HTTP_422_UNPROCESSABLE_ENTITY,
                detail={
                    "error_code": "UNACHIEVABLE_TARGET_RETURN",
                    "detail": "Target Return Unachievable!",
                    "explanation": "Your target return is above the maximum optimal target return for the specified companies and constraints",
                    "fix": f"Decrease your target return to {max_sharpe_return}% or below"
                }
            )

    # Handle date range errors
    if "Date range" in error_message and "must be provided" in error_message:
        return HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=create_error_response(
                issue=ErrorTypes.MISSING_DATE_RANGE,
                description="Date range is required for custom portfolios.",
                fix="Please provide both from_date and to_date parameters.",
                status_code=status.HTTP_400_BAD_REQUEST
            )
        )

    # Handle risk-free rate errors
    if "Risk-free rate" in error_message:
        return HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=create_error_response(
                issue=ErrorTypes.INVALID_RISK_FREE_RATE,
                description="The risk-free rate is not valid.",
                fix="Please enter a valid risk-free rate between -5% and 15% (-0.05 to 0.15 in decimal form).",
                status_code=status.HTTP_400_BAD_REQUEST
            )
        )

    # Handle constraint set errors
    if "Constraint set" in error_message:
        return HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=create_error_response(
                issue=ErrorTypes.INVALID_CONSTRAINT_SET,
                description="The weight constraints are not valid.",
                fix="Please provide valid weight constraints as [min_weight, max_weight] where min_weight ≥ 0 and max_weight ≤ 1.",
                status_code=status.HTTP_400_BAD_REQUEST
            )
        )

    # Handle rebalance frequency errors
    if "Rebalance frequency" in error_message:
        return HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=create_error_response(
                issue=ErrorTypes.INVALID_REBALANCE_FREQUENCY,
                description="The rebalance frequency is not valid.",
                fix="Please select a valid rebalance frequency (MONTHLY, QUARTERLY, SEMI-ANNUALLY, or ANNUALLY).",
                status_code=status.HTTP_400_BAD_REQUEST
            )
        )

    # Handle investment amount errors
    if "Investment amount" in error_message:
        return HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=create_error_response(
                issue=ErrorTypes.INVALID_INVESTMENT_AMOUNT,
                description="The investment amount is not valid.",
                fix="Please enter a positive investment amount less than 1 billion LKR.",
                status_code=status.HTTP_400_BAD_REQUEST
            )
        )

    # Handle portfolio name errors
    if "Portfolio name" in error_message:
        return HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=create_error_response(
                issue=ErrorTypes.INVALID_PORTFOLIO_NAME,
                description="The portfolio name is not valid.",
                fix="Please provide a non-empty portfolio name.",
                status_code=status.HTTP_400_BAD_REQUEST
            )
        )

    # Handle general validation errors
    if "validation" in error_message.lower():
        return HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=create_error_response(
                issue=ErrorTypes.VALIDATION_ERROR,
                description=error_message,
                fix="Please review your portfolio settings and try again.",
                status_code=status.HTTP_400_BAD_REQUEST
            )
        )

    # Handle server errors
    logger.error("Unexpected error during portfolio creation: %s", error_message)
    return HTTPException(
        status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
        detail=create_error_response(
            issue=ErrorTypes.SERVER_ERROR,
            description="An unexpected error occurred during portfolio creation.",
            fix="Please try again later or contact support if the problem persists.",
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR
        )
    )

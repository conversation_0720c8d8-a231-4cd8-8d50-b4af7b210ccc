"""
Error Handling Example

This script demonstrates how to handle errors from the CSE Portfolio Builder API.
"""

import asyncio
import httpx
import json
from typing import Dict, Any, Optional, List, Tuple


class APIClient:
    """A simple API client for the CSE Portfolio Builder API."""
    
    def __init__(self, base_url: str = "http://localhost:8000"):
        """Initialize the API client.
        
        Args:
            base_url: The base URL of the API
        """
        self.base_url = base_url
        self.client = httpx.AsyncClient(timeout=30.0)
    
    async def close(self):
        """Close the HTTP client."""
        await self.client.aclose()
    
    async def handle_response(self, response: httpx.Response) -> Tuple[bool, Dict[str, Any]]:
        """Handle the API response and extract error information if needed.
        
        Args:
            response: The HTTP response
            
        Returns:
            Tuple of (success, data)
        """
        try:
            data = response.json()
            
            # Check if the response is successful
            if response.status_code < 400:
                return True, data
            
            # Extract error information
            error_detail = data.get("detail", {})
            if isinstance(error_detail, str):
                # Handle simple error format
                error_message = error_detail
                error_code = "UNKNOWN_ERROR"
                status_code = response.status_code
                errors = None
            else:
                # Handle detailed error format
                error_message = error_detail.get("detail", "Unknown error")
                error_code = error_detail.get("error_code", "UNKNOWN_ERROR")
                status_code = error_detail.get("status_code", response.status_code)
                errors = error_detail.get("errors")
            
            # Create a standardized error object
            error_data = {
                "success": False,
                "error_message": error_message,
                "error_code": error_code,
                "status_code": status_code,
                "errors": errors,
                "raw_response": data
            }
            
            return False, error_data
            
        except Exception as e:
            # Handle non-JSON responses or other errors
            return False, {
                "success": False,
                "error_message": f"Failed to parse response: {str(e)}",
                "error_code": "RESPONSE_PARSING_ERROR",
                "status_code": response.status_code,
                "errors": None,
                "raw_response": response.text
            }
    
    async def get_portfolio(self, portfolio_id: str) -> Tuple[bool, Dict[str, Any]]:
        """Get a portfolio by ID.
        
        Args:
            portfolio_id: The ID of the portfolio
            
        Returns:
            Tuple of (success, data)
        """
        url = f"{self.base_url}/api/portfolios/{portfolio_id}"
        response = await self.client.get(url)
        return await self.handle_response(response)
    
    async def get_companies_by_industry(self, industry_groups: List[str]) -> Tuple[bool, Dict[str, Any]]:
        """Get companies by industry groups.
        
        Args:
            industry_groups: List of industry group names
            
        Returns:
            Tuple of (success, data)
        """
        url = f"{self.base_url}/api/portfolio/companies-by-industry"
        response = await self.client.post(url, json={"industry_groups": industry_groups})
        return await self.handle_response(response)
    
    async def get_sectors(self, ticker_symbols: List[str]) -> Tuple[bool, Dict[str, Any]]:
        """Get sectors for specific companies.
        
        Args:
            ticker_symbols: List of ticker symbols
            
        Returns:
            Tuple of (success, data)
        """
        url = f"{self.base_url}/api/portfolio/sectors"
        response = await self.client.post(url, json={"ticker_symbols": ticker_symbols})
        return await self.handle_response(response)


async def demonstrate_error_handling():
    """Demonstrate how to handle different types of errors."""
    client = APIClient()
    
    try:
        print("Example 1: Invalid Portfolio ID Format")
        print("-" * 50)
        success, data = await client.get_portfolio("invalid-id")
        
        if not success:
            print(f"Error: {data['error_message']}")
            print(f"Error Code: {data['error_code']}")
            print(f"Status Code: {data['status_code']}")
            print()
        
        print("Example 2: Non-existent Portfolio ID")
        print("-" * 50)
        success, data = await client.get_portfolio("PF-999999-nonexistent")
        
        if not success:
            print(f"Error: {data['error_message']}")
            print(f"Error Code: {data['error_code']}")
            print(f"Status Code: {data['status_code']}")
            print()
        
        print("Example 3: Empty Industry Groups List")
        print("-" * 50)
        success, data = await client.get_companies_by_industry([])
        
        if not success:
            print(f"Error: {data['error_message']}")
            print(f"Error Code: {data['error_code']}")
            print(f"Status Code: {data['status_code']}")
            
            # Check for validation errors
            if data['errors']:
                print("Validation Errors:")
                for error in data['errors']:
                    field_path = ".".join(error.get("loc", []))
                    print(f"  - {field_path}: {error.get('msg')}")
            print()
        
        print("Example 4: Non-existent Industry Group")
        print("-" * 50)
        success, data = await client.get_companies_by_industry(["NON_EXISTENT_GROUP"])
        
        if not success:
            print(f"Error: {data['error_message']}")
            print(f"Error Code: {data['error_code']}")
            print(f"Status Code: {data['status_code']}")
            print()
        
        print("Example 5: Empty Ticker Symbols List")
        print("-" * 50)
        success, data = await client.get_sectors([])
        
        if not success:
            print(f"Error: {data['error_message']}")
            print(f"Error Code: {data['error_code']}")
            print(f"Status Code: {data['status_code']}")
            print()
        
        print("Example 6: Non-existent Ticker Symbol")
        print("-" * 50)
        success, data = await client.get_sectors(["NON_EXISTENT_TICKER"])
        
        if not success:
            print(f"Error: {data['error_message']}")
            print(f"Error Code: {data['error_code']}")
            print(f"Status Code: {data['status_code']}")
            print()
        
        print("Example 7: Successful Request")
        print("-" * 50)
        success, data = await client.get_sectors(["LOLC.N0000", "JKH.N0000"])
        
        if success:
            print("Request successful!")
            print(f"Message: {data.get('message')}")
            print(f"Sectors: {json.dumps(data.get('sectors'), indent=2)}")
        else:
            print(f"Error: {data['error_message']}")
        
    finally:
        await client.close()


if __name__ == "__main__":
    asyncio.run(demonstrate_error_handling())

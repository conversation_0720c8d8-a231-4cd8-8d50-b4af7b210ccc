"""
Trade Repository

This module provides repository class for trade-related database operations.
"""

import logging
from datetime import datetime, time
from typing import Dict, List, Optional, Any, cast, Union
import uuid
from motor.motor_asyncio import AsyncIOMotorDatabase

from app.db.session import get_database  # Import the database connection function
from app.utils.db_utils import safe_mongodb_operation

# Configure logging
logger = logging.getLogger(__name__)

class TradeRepository:
    """Repository for trade-related database operations."""

    @staticmethod
    async def get_historical_trades(ticker_symbol: str, start_date: str, end_date: str) -> List[Dict[str, Any]]:
        """
        Get historical trade data for a specific ticker symbol within a date range.

        Args:
            ticker_symbol: The ticker symbol to retrieve data for
            start_date: Start date for the data range
            end_date: End date for the data range

        Returns:
            List of trade data dictionaries
        """
        try:
            # Get the database for the current event loop
            db: AsyncIOMotorDatabase = await get_database()

            # Query MongoDB for this ticker's historical data
            trades_result = await safe_mongodb_operation(
                lambda: db["trades"].find(
                    {
                        "symbol": ticker_symbol,
                        "tradeDate": {"$gte": start_date, "$lte": end_date}
                    },
                    {
                        "tradeDate": 1,
                        "close": 1,
                        "_id": 0
                    }
                ).sort("tradeDate", 1).to_list(length=None)  # Sort by date in ascending order
            )

            trades = cast(List[Dict[str, Any]], trades_result)
            if not trades:
                logger.warning(f"No trade data found for {ticker_symbol} in the specified date range")
                return []

            logger.info(f"Retrieved {len(trades)} data points for {ticker_symbol}")

            # Convert Decimal128 values to float
            for trade in trades:
                if "close" in trade and hasattr(trade["close"], 'to_decimal'):
                    trade["close"] = float(trade["close"].to_decimal())

            return trades
        except Exception as e:
            logger.error(f"Error retrieving historical trades for {ticker_symbol}: {str(e)}")
            return []

    @staticmethod
    async def check_trade_data_exists(date_or_ticker: Union[datetime, str]) -> int:
        """
        Check if trade data exists for a specific date or ticker symbol.

        Args:
            date_or_ticker: Either a datetime object (to check for a specific date) or a string (to check for a specific ticker)

        Returns:
            Number of records found (0 if none exist)
        """
        try:
            # Get the database for the current event loop
            db: AsyncIOMotorDatabase = await get_database()

            # Determine if we're checking by date or by ticker
            if isinstance(date_or_ticker, datetime):
                # Check by date - create start and end of day
                start_of_day = datetime.combine(date_or_ticker.date(), time.min)
                end_of_day = datetime.combine(date_or_ticker.date(), time.max)

                # Count records for the date
                count_result = await safe_mongodb_operation(
                    lambda: db["trades"].count_documents({
                        "tradeDate": {"$gte": start_of_day, "$lte": end_of_day}
                    }),
                    fallback_value=0
                )

                count = cast(int, count_result)
                logger.debug(f"Found {count} trade records for date {date_or_ticker.date().isoformat()}")
                return count
            else:
                # Check by ticker symbol
                ticker_symbol = date_or_ticker

                # Count records for the ticker symbol
                count_result = await safe_mongodb_operation(
                    lambda: db["trades"].count_documents({"symbol": ticker_symbol}),
                    fallback_value=0
                )

                count = cast(int, count_result)
                logger.debug(f"Found {count} trade records for ticker {ticker_symbol}")
                return count
        except Exception as e:
            logger.error(f"Error checking for trade data existence: {str(e)}")
            return 0

    @staticmethod
    async def insert_trade_data(trade_data: Dict[str, Any]) -> bool:
        """
        Insert trade data into the database.

        Args:
            trade_data: Dictionary containing trade data

        Returns:
            True if insertion was successful, False otherwise
        """
        try:
            # Set _id to deterministic uuid if not already present
            if "_id" not in trade_data:
                # Use id and tradeDate for uuid generation
                id_val = trade_data.get("id")
                trade_date_val = trade_data.get("tradeDate")
                unique_id = str(uuid.uuid5(uuid.NAMESPACE_DNS, f"{id_val}_{trade_date_val}"))
                trade_data["_id"] = unique_id
            # Remove uuid field if present
            if "uuid" in trade_data:
                del trade_data["uuid"]

            # Get the database for the current event loop
            db: AsyncIOMotorDatabase = await get_database()

            # Insert the trade data
            result = await safe_mongodb_operation(
                lambda: db["trades"].insert_one(trade_data)
            )
            return result is not None
        except Exception as e:
            logger.error(f"Error inserting trade data: {str(e)}")
            raise

    @staticmethod
    async def get_latest_trade(ticker_symbol: str) -> Optional[Dict[str, Any]]:
        """
        Get the latest trade data for a specific ticker symbol.

        Args:
            ticker_symbol: The ticker symbol to retrieve data for

        Returns:
            Latest trade data dictionary or None if not found
        """
        try:
            # Get the database for the current event loop
            db: AsyncIOMotorDatabase = await get_database()

            # Query MongoDB for this ticker's latest trade data
            trade_result = await safe_mongodb_operation(
                lambda: db["trades"].find(
                    {"symbol": ticker_symbol}
                ).sort("tradeDate", -1).limit(1).to_list(length=None)  # Sort by date in descending order, limit to 1
            )

            trade = cast(List[Dict[str, Any]], trade_result)
            if not trade or len(trade) == 0:
                logger.warning(f"No trade data found for {ticker_symbol}")
                return None

            # Convert Decimal128 values to float
            if "close" in trade[0] and hasattr(trade[0]["close"], 'to_decimal'):
                trade[0]["close"] = float(trade[0]["close"].to_decimal())

            return trade[0]
        except Exception as e:
            logger.error(f"Error retrieving latest trade for {ticker_symbol}: {str(e)}")
            return None

    @staticmethod
    async def upsert_trade_data(unique_id: str, trade_data: Dict[str, Any]) -> bool:
        """
        Upsert trade data into the database.
        For time-series collections, we need to delete existing record and insert new one.

        Args:
            unique_id: The unique identifier for the trade record
            trade_data: Dictionary containing trade data

        Returns:
            True if upsert was successful, False otherwise
        """
        try:
            db: AsyncIOMotorDatabase = await get_database()
            # For time-series collections, we need to delete existing record first
            await safe_mongodb_operation(
                lambda: db["trades"].delete_one({"_id": unique_id})
            )

            # Then insert the new record
            result = await safe_mongodb_operation(
                lambda: db["trades"].insert_one(trade_data)
            )
            return result is not None
        except Exception as e:
            logger.error(f"Error upserting trade data: {str(e)}")
            raise

    @staticmethod
    async def bulk_upsert_trade_data(trade_data_list: List[Dict[str, Any]]) -> bool:
        """
        Bulk upsert trade data into the database.
        For time-series collections, we need to delete existing records and insert new ones.

        Args:
            trade_data_list: List of dictionaries containing trade data

        Returns:
            True if bulk upsert was successful, False otherwise
        """
        try:
            if not trade_data_list:
                return True

            # Extract unique IDs for deletion
            unique_ids = [doc["_id"] for doc in trade_data_list if "_id" in doc]

            # Get the database for the current event loop
            db = await get_database()

            # Delete existing records in bulk
            if unique_ids:
                await safe_mongodb_operation(
                    lambda: db["trades"].delete_many({"_id": {"$in": unique_ids}})
                )

            # Insert new records in bulk
            result = await safe_mongodb_operation(
                lambda: db["trades"].insert_many(trade_data_list)
            )
            return result is not None
        except Exception as e:
            logger.error(f"Error bulk upserting trade data: {str(e)}")
            raise

    @staticmethod
    async def get_trades(
        query: Dict[str, Any],
        sort: tuple = ("symbol", 1),
        skip: int = 0,
        limit: int = 50
    ) -> List[Dict[str, Any]]:
        """
        Get trades with pagination and sorting.

        Args:
            query: MongoDB query dictionary
            sort: Tuple of (field, direction) for sorting
            skip: Number of records to skip
            limit: Maximum number of records to return

        Returns:
            List of trade data dictionaries
        """
        try:
            # Get the database for the current event loop
            db = await get_database()

            # Query MongoDB with pagination and sorting
            trades = await safe_mongodb_operation(
                lambda: db["trades"].find(query)
                .sort(sort[0], sort[1])
                .skip(skip)
                .limit(limit)
                .to_list(length=None)
            )

            if not trades:
                return []

            # Convert the Future[list[Unknown]] to List[Dict[str, Any]]
            return [dict(trade) for trade in trades]
        except Exception as e:
            logger.error("Error retrieving trades: %s", str(e))
            raise

    @staticmethod
    async def count_trades_by_symbol(ticker_symbol: str) -> int:
        """
        Count the number of trade records for a specific ticker symbol.

        Args:
            ticker_symbol: The ticker symbol to count records for

        Returns:
            Number of trade records for the ticker symbol
        """
        try:
            # Get the database for the current event loop
            db = await get_database()

            # Count records for the ticker symbol
            count = await safe_mongodb_operation(
                lambda: db["trades"].count_documents({"symbol": ticker_symbol}),
                fallback_value=0
            )

            return cast(int, count)
        except Exception as e:
            logger.error(f"Error counting trades for {ticker_symbol}: {str(e)}")
            return 0

    @staticmethod
    async def get_trade_date_range(ticker_symbol: str) -> tuple[Optional[str], Optional[str], int]:
        """
        Get the earliest and latest trade dates for a specific ticker symbol.

        Args:
            ticker_symbol: The ticker symbol to get date range for

        Returns:
            Tuple of (earliest_date, latest_date, count)
        """
        try:
            # Get the database for the current event loop
            db: AsyncIOMotorDatabase = await get_database()

            # Get the earliest trade date
            earliest_trade_result = await safe_mongodb_operation(
                lambda: db["trades"].find(
                    {"symbol": ticker_symbol}
                ).sort("tradeDate", 1).limit(1).to_list(length=None)
            )

            # Get the latest trade date
            latest_trade_result = await safe_mongodb_operation(
                lambda: db["trades"].find(
                    {"symbol": ticker_symbol}
                ).sort("tradeDate", -1).limit(1).to_list(length=None)
            )

            # Count records for the ticker symbol
            count_result = await safe_mongodb_operation(
                lambda: db["trades"].count_documents({"symbol": ticker_symbol}),
                fallback_value=0
            )

            earliest_trade = cast(List[Dict[str, Any]], earliest_trade_result)
            latest_trade = cast(List[Dict[str, Any]], latest_trade_result)
            count = cast(int, count_result)

            earliest_date = earliest_trade[0]["tradeDate"] if earliest_trade and len(earliest_trade) > 0 else None
            latest_date = latest_trade[0]["tradeDate"] if latest_trade and len(latest_trade) > 0 else None

            return earliest_date, latest_date, count
        except Exception as e:
            logger.error(f"Error getting trade date range for {ticker_symbol}: {str(e)}")
            return None, None, 0

    @staticmethod
    async def get_trades_by_symbol_and_date_range(ticker_symbol: str, from_date: datetime, to_date: datetime) -> List[Dict]:
        """
        Get all trades for a specific ticker symbol within a date range.

        Args:
            ticker_symbol: The ticker symbol to get trades for
            from_date: Start date for the query
            to_date: End date for the query

        Returns:
            List of trade documents
        """
        try:
            # Get the database for the current event loop
            db: AsyncIOMotorDatabase = await get_database()

            # Query for trades within the date range
            query = {
                "symbol": ticker_symbol,
                "tradeDate": {
                    "$gte": from_date,
                    "$lte": to_date
                }
            }

            # Get all trades matching the query
            trades = await safe_mongodb_operation(
                lambda: db["trades"].find(query).sort("tradeDate", 1).to_list(length=None)
            )

            return cast(List[Dict], [dict(trade) for trade in trades]) if trades else []
        except Exception as e:
            logger.error(f"Error getting trades for {ticker_symbol} in date range: {str(e)}")
            return []

"""
Portfolio Rebalance Service

This module provides functionality to rebalance portfolios based on their rebalance frequency.
It handles checking if a portfolio needs rebalancing and performing the rebalancing operation.
"""

import logging
from datetime import datetime, timedelta
from typing import Dict, Any, Tuple, Optional
from zoneinfo import ZoneInfo
from dateutil.relativedelta import relativedelta

from app.db.repositories import PortfolioRepository
from app.utils.db_utils import safe_decimal128
from app.utils.date_utils import skip_if_weekend
from app.services.portfolio_service import (
    create_min_variance_portfolio,
    create_max_sharpe_portfolio,
    create_custom_portfolio
)
from app.services.portfolio_storage_service import _calculate_allocations, _fetch_latest_trades
from app.config import (
    PORTFOLIO_TYPE_MIN_VARIANCE,
    PORTFOLIO_TYPE_MAX_SHARPE,
    PORTFOLIO_TYPE_CUSTOM
)

# Constants
DEFAULT_TIMEZONE = "Asia/Colombo"
DEFAULT_WINDOW_DAYS = 5 * 365  # 5 years in days

# Configure logging
logger = logging.getLogger(__name__)

async def check_portfolios_for_rebalancing(force_rebalance: bool = False) -> Dict[str, Any]:
    """
    Check all portfolios to see if any need rebalancing based on their rebalance frequency.

    Args:
        force_rebalance: If True, rebalance even on weekends. Default is False.

    Returns:
        Dictionary containing rebalance operation summary
    """
    rebalance_timestamp = datetime.now(ZoneInfo(DEFAULT_TIMEZONE))

    # Check if today is a weekend
    weekend_skip = skip_if_weekend(rebalance_timestamp, "portfolio rebalance check", force=force_rebalance)
    if weekend_skip:
        # Add service-specific fields to the standard weekend skip response
        weekend_skip.update({
            "portfolios_checked": 0,
            "portfolios_rebalanced": 0,
            "portfolios_failed": 0
        })
        return weekend_skip

    try:
        # Get all portfolios
        portfolios = await PortfolioRepository.get_all_portfolios()

        if not portfolios:
            logger.info("No active portfolios found to check for rebalancing")
            return {
                "success": True,
                "message": "No active portfolios found to check for rebalancing",
                "portfolios_checked": 0,
                "portfolios_rebalanced": 0,
                "portfolios_failed": 0,
                "timestamp": rebalance_timestamp.isoformat()
            }

        # Track rebalance statistics
        checked_count = 0
        rebalanced_count = 0
        failed_count = 0
        portfolio_rebalances = []

        # Check each portfolio
        for portfolio in portfolios:
            checked_count += 1
            portfolio_id = portfolio.get("portfolioId")

            try:
                # Check if portfolio needs rebalancing
                needs_rebalancing, reason = check_if_portfolio_needs_rebalancing(portfolio)

                if needs_rebalancing:
                    # Rebalance the portfolio
                    result = await rebalance_portfolio(portfolio)

                    if result.get("success"):
                        rebalanced_count += 1
                        portfolio_rebalances.append({
                            "portfolioId": portfolio_id,
                            "status": "rebalanced",
                            "reason": reason,
                            "details": result
                        })
                    else:
                        failed_count += 1
                        portfolio_rebalances.append({
                            "portfolioId": portfolio_id,
                            "status": "failed",
                            "reason": reason,
                            "error": result.get("message")
                        })
                else:
                    # Portfolio doesn't need rebalancing
                    portfolio_rebalances.append({
                        "portfolioId": portfolio_id,
                        "status": "skipped",
                        "reason": "Not due for rebalancing"
                    })
            except Exception as e:
                logger.error(f"Error checking/rebalancing portfolio {portfolio_id}: {str(e)}")
                failed_count += 1
                portfolio_rebalances.append({
                    "portfolioId": portfolio_id,
                    "status": "failed",
                    "error": str(e)
                })

        # Return summary
        return {
            "success": True,
            "message": f"Checked {checked_count} portfolios, rebalanced {rebalanced_count}, failed {failed_count}",
            "portfolios_checked": checked_count,
            "portfolios_rebalanced": rebalanced_count,
            "portfolios_failed": failed_count,
            "rebalance_details": portfolio_rebalances,
            "timestamp": rebalance_timestamp.isoformat()
        }

    except Exception as e:
        logger.error(f"Error in portfolio rebalance check: {str(e)}")
        return {
            "success": False,
            "message": f"Portfolio rebalance check failed: {str(e)}",
            "error_code": "REBALANCE_CHECK_FAILED",
            "portfolios_checked": 0,
            "portfolios_rebalanced": 0,
            "portfolios_failed": 0,
            "timestamp": rebalance_timestamp.isoformat()
        }

def check_if_portfolio_needs_rebalancing(portfolio: Dict[str, Any]) -> Tuple[bool, str]:
    """
    Check if a portfolio needs rebalancing based on its rebalance frequency and last rebalanced date.

    Args:
        portfolio: Portfolio document from the database

    Returns:
        Tuple of (needs_rebalancing, reason)
    """
    portfolio_id = portfolio.get("portfolioId")
    rebalance_frequency = portfolio.get("rebalanceFrequency", "ANNUALLY")
    last_rebalanced_date = portfolio.get("lastRebalancedDate")

    if not last_rebalanced_date:
        logger.warning(f"Portfolio {portfolio_id} has no last rebalanced date, using creation date")
        last_rebalanced_date = portfolio.get("creationDate")

    # Ensure last_rebalanced_date has timezone info
    if last_rebalanced_date.tzinfo is None:
        # Convert naive datetime to aware datetime with the default timezone
        last_rebalanced_date = last_rebalanced_date.replace(tzinfo=ZoneInfo(DEFAULT_TIMEZONE))

    current_date = datetime.now(ZoneInfo(DEFAULT_TIMEZONE))

    # Calculate the next rebalance date based on frequency
    if rebalance_frequency == "MONTHLY":
        # Add one month to last rebalanced date
        next_rebalance_date = last_rebalanced_date + relativedelta(months=1)
        reason = "Monthly rebalance due"
    elif rebalance_frequency == "QUARTERLY":
        # Add three months to last rebalanced date
        next_rebalance_date = last_rebalanced_date + relativedelta(months=3)
        reason = "Quarterly rebalance due"
    elif rebalance_frequency == "SEMI-ANNUALLY":
        # Add six months to last rebalanced date
        next_rebalance_date = last_rebalanced_date + relativedelta(months=6)
        reason = "Semi-annual rebalance due"
    else:  # ANNUALLY (default)
        # Add one year to last rebalanced date
        next_rebalance_date = last_rebalanced_date + relativedelta(years=1)
        reason = "Annual rebalance due"

    # Check if current date is past the next rebalance date
    needs_rebalancing = current_date >= next_rebalance_date

    logger.debug(f"Portfolio {portfolio_id} rebalance check: frequency={rebalance_frequency}, "
                f"last_rebalanced={last_rebalanced_date}, next_rebalance={next_rebalance_date}, "
                f"needs_rebalancing={needs_rebalancing}")

    return needs_rebalancing, reason

async def rebalance_portfolio(portfolio: Dict[str, Any]) -> Dict[str, Any]:
    """
    Rebalance a portfolio by recalculating optimal allocations and adjusting holdings.

    Args:
        portfolio: Portfolio document from the database

    Returns:
        Dictionary containing rebalance operation result
    """
    portfolio_id = portfolio.get("portfolioId")
    portfolio_type = portfolio.get("portfolioType")
    rebalance_timestamp = datetime.now(ZoneInfo(DEFAULT_TIMEZONE))

    try:
        logger.info(f"Starting rebalance for portfolio {portfolio_id} of type {portfolio_type}")

        # Extract constraints from the portfolio
        constraints = {}
        if "constraints" in portfolio:
            portfolio_constraints = portfolio.get("constraints", {})

            # Different approach based on portfolio type
            if portfolio_type == PORTFOLIO_TYPE_CUSTOM:
                # For custom portfolios, implement sliding window approach
                original_start_date = portfolio_constraints.get("start_date")
                original_end_date = portfolio_constraints.get("end_date")

                # If both dates exist, calculate the window length
                if original_start_date and original_end_date:
                    try:
                        # Convert string dates to datetime objects if needed
                        if isinstance(original_start_date, str):
                            original_start = datetime.fromisoformat(original_start_date.replace('Z', '+00:00'))
                        else:
                            original_start = original_start_date

                        if isinstance(original_end_date, str):
                            original_end = datetime.fromisoformat(original_end_date.replace('Z', '+00:00'))
                        else:
                            original_end = original_end_date

                        # Calculate window length
                        window_length = (original_end - original_start).days

                        # Create a new window of the same length ending today
                        new_end_date = datetime.now(ZoneInfo(DEFAULT_TIMEZONE))
                        new_start_date = new_end_date - timedelta(days=window_length)

                        # Update the constraints with the new window
                        start_date_str = new_start_date.strftime("%Y-%m-%d")
                        end_date_str = new_end_date.strftime("%Y-%m-%d")

                        # Set both the original and new naming conventions for compatibility
                        constraints["start_date"] = start_date_str
                        constraints["from_date"] = start_date_str
                        constraints["end_date"] = end_date_str
                        constraints["to_date"] = end_date_str

                        logger.info(f"Applied sliding window for custom portfolio {portfolio_id}: {new_start_date.strftime('%Y-%m-%d')} to {new_end_date.strftime('%Y-%m-%d')}")
                    except Exception as e:
                        logger.error(f"Error applying sliding window: {str(e)}. Falling back to default {DEFAULT_WINDOW_DAYS//365}-year window.")
                        # Fall back to default window
                        new_end_date = datetime.now(ZoneInfo(DEFAULT_TIMEZONE))
                        new_start_date = new_end_date - timedelta(days=DEFAULT_WINDOW_DAYS)

                        start_date_str = new_start_date.strftime("%Y-%m-%d")
                        end_date_str = new_end_date.strftime("%Y-%m-%d")

                        # Set both the original and new naming conventions for compatibility
                        constraints["start_date"] = start_date_str
                        constraints["from_date"] = start_date_str
                        constraints["end_date"] = end_date_str
                        constraints["to_date"] = end_date_str
                else:
                    # If original dates don't exist, use default window
                    new_end_date = datetime.now(ZoneInfo(DEFAULT_TIMEZONE))
                    new_start_date = new_end_date - timedelta(days=DEFAULT_WINDOW_DAYS)

                    start_date_str = new_start_date.strftime("%Y-%m-%d")
                    end_date_str = new_end_date.strftime("%Y-%m-%d")

                    # Set both the original and new naming conventions for compatibility
                    constraints["start_date"] = start_date_str
                    constraints["from_date"] = start_date_str
                    constraints["end_date"] = end_date_str
                    constraints["to_date"] = end_date_str
            else:
                # For default portfolios (min_variance, max_sharpe), let the service functions
                # apply their default constraints (which already use current date)
                # We don't need to set date constraints here
                pass

            # Copy other constraints (for all portfolio types)
            if "risk_free_rate" in portfolio_constraints:
                constraints["risk_free_rate"] = portfolio_constraints["risk_free_rate"]
            if "weight_bounds" in portfolio_constraints:
                constraints["weight_bounds"] = portfolio_constraints["weight_bounds"]

        # Get new optimal allocations based on portfolio type
        new_allocations = {}
        performance = {}

        if portfolio_type == PORTFOLIO_TYPE_MIN_VARIANCE:
            # Create a new minimum variance portfolio
            result = await create_min_variance_portfolio(constraints)
            min_variance_allocations = result.get("allocations", {})
            new_allocations = {ticker: weight / 100 for ticker, weight in min_variance_allocations.items() if weight > 0}
            performance = result.get("performance", {})

        elif portfolio_type == PORTFOLIO_TYPE_MAX_SHARPE:
            # Create a new maximum Sharpe ratio portfolio
            result = await create_max_sharpe_portfolio(constraints)
            max_sharpe_allocations = result.get("allocations", {})
            new_allocations = {ticker: weight / 100 for ticker, weight in max_sharpe_allocations.items() if weight > 0}
            performance = result.get("performance", {})

        elif portfolio_type == PORTFOLIO_TYPE_CUSTOM:
            # Create a new custom portfolio
            companies_dict = {}
            if "constraints" in portfolio and "companies" in portfolio["constraints"]:
                # Extract company names from constraints
                company_list = portfolio["constraints"]["companies"]

                # We need to map these back to ticker symbols
                # This is a simplification - in a real implementation, you'd need to fetch the actual mapping
                for company_name in company_list:
                    # Find the ticker for this company in the holdings
                    for holding in portfolio.get("holdings", []):
                        if holding.get("companyName") == company_name:
                            companies_dict[company_name] = holding.get("tickerSymbol")
                            break

            # If we couldn't extract companies from constraints, use current holdings
            if not companies_dict:
                for holding in portfolio.get("holdings", []):
                    companies_dict[holding.get("companyName")] = holding.get("tickerSymbol")

            # Get target return from constraints or use current return
            target_return = None
            if "constraints" in portfolio and "target_return" in portfolio["constraints"]:
                target_return = portfolio["constraints"]["target_return"]

            if target_return is None:
                # Use current annualized return as target, or default to 0 if not present
                annualized_return = portfolio.get("annualizedReturn")
                target_return = float(annualized_return.to_decimal()) if annualized_return is not None else 0.0

            # Create a new custom portfolio
            result = await create_custom_portfolio(
                companies_dict=companies_dict,
                constraints=constraints,
                target_return=target_return
            )

            custom_allocations = result.get("allocations", {})
            new_allocations = {ticker: weight / 100 for ticker, weight in custom_allocations.items() if weight > 0}
            performance = result.get("performance", {})

        else:
            raise ValueError(f"Unsupported portfolio type: {portfolio_type}")

        # Apply the new allocations to the portfolio
        await apply_rebalance(
            portfolio_id=portfolio_id,
            new_allocations=new_allocations,
            performance=performance,
            rebalance_timestamp=rebalance_timestamp
        )

        return {
            "success": True,
            "message": f"Portfolio {portfolio_id} rebalanced successfully",
            "new_allocations": new_allocations,
            "performance": performance,
            "rebalance_timestamp": rebalance_timestamp.isoformat()
        }

    except Exception as e:
        logger.error(f"Error rebalancing portfolio {portfolio_id}: {str(e)}")
        return {
            "success": False,
            "message": f"Failed to rebalance portfolio {portfolio_id}: {str(e)}",
            "error_code": "REBALANCE_FAILED",
            "rebalance_timestamp": rebalance_timestamp.isoformat()
        }

async def apply_rebalance(
    portfolio_id: str,
    new_allocations: Dict[str, float],
    performance: Dict[str, float],
    rebalance_timestamp: datetime
) -> bool:
    """
    Apply new allocations to a portfolio during rebalancing.

    Args:
        portfolio_id: Portfolio ID
        new_allocations: Dictionary mapping ticker symbols to new allocation weights (0-1)
        performance: Dictionary containing performance metrics
        rebalance_timestamp: Timestamp of the rebalance operation

    Returns:
        True if rebalance was applied successfully, False otherwise
    """
    try:
        # Get the portfolio
        portfolio = await PortfolioRepository.get_portfolio(portfolio_id)

        if not portfolio:
            logger.error(f"Cannot rebalance: Portfolio {portfolio_id} not found")
            return False

        # Get the current investment amount
        investment_amount = float(portfolio.get("totalMarketValue").to_decimal())

        # Get all tickers
        tickers = list(new_allocations.keys())

        # Fetch latest trade data for all tickers
        latest_trades = await _fetch_latest_trades(tickers)

        # Calculate allocations with integer quantities
        adjusted_allocations = await _calculate_allocations(new_allocations, latest_trades, investment_amount)

        # Prepare new holdings data
        new_holdings = []
        total_market_value = 0

        # Create holdings with adjusted quantities and weights
        for ticker, data in adjusted_allocations.items():
            quantity = data["quantity"]
            last_traded_price = data["price"]
            market_value = quantity * last_traded_price

            # Skip if quantity is zero
            if quantity <= 0:
                continue

            # Recalculate weight based on actual market value
            actual_weight = market_value / investment_amount

            # Get company name from existing holdings or use ticker as fallback
            company_name = ticker
            for holding in portfolio.get("holdings", []):
                if holding.get("tickerSymbol") == ticker:
                    company_name = holding.get("companyName")
                    break

            # Create the holding
            holding = {
                "companyName": company_name,
                "tickerSymbol": ticker,
                "quantity": safe_decimal128(quantity),
                "lastTradedPrice": safe_decimal128(last_traded_price),
                "totalCost": safe_decimal128(market_value),  # Use current market value as new cost basis
                "marketValue": safe_decimal128(market_value),
                "unrealizedGainLoss": safe_decimal128(0),  # Reset to zero after rebalance
                "weight": safe_decimal128(actual_weight),
                "annualizedReturn": safe_decimal128(0),  # This would need to be calculated properly
                "annualizedVolatility": safe_decimal128(0)  # This would need to be calculated properly
            }

            new_holdings.append(holding)
            total_market_value += market_value

        # Update the portfolio with new holdings and performance metrics
        update_result = await PortfolioRepository.update_portfolio_after_rebalance(
            portfolio_id=portfolio_id,
            new_holdings=new_holdings,
            total_market_value=safe_decimal128(total_market_value),
            annualized_return=safe_decimal128(performance.get("returns", 0)),
            annualized_volatility=safe_decimal128(performance.get("volatility", 0)),
            last_rebalanced_date=rebalance_timestamp
        )

        return update_result

    except Exception as e:
        logger.error(f"Error applying rebalance to portfolio {portfolio_id}: {str(e)}")
        return False

async def manual_check_portfolios_for_rebalancing(force_rebalance: bool = False) -> Dict[str, Any]:
    """
    Manually trigger the portfolio rebalance check.

    This function is useful for testing the rebalance functionality without waiting for the scheduled time.
    It runs the rebalance check directly in the current event loop.

    Args:
        force_rebalance: If True, rebalance even on weekends. Default is False.

    Returns:
        Result of the rebalance check operation
    """
    logger.info("Manual trigger: Running portfolio rebalance check")
    try:
        result = await check_portfolios_for_rebalancing(force_rebalance)
        return result
    except Exception as e:
        error_msg = f"Error executing manual rebalance check: {str(e)}"
        logger.error(error_msg)
        return {"error": True, "message": error_msg}

async def manual_rebalance_portfolio(portfolio_id: Optional[str] = None, force_rebalance: bool = False) -> Dict[str, Any]:
    """
    Manually trigger portfolio rebalancing for a specific portfolio or all portfolios.

    Args:
        portfolio_id: ID of the portfolio to rebalance. If None, all portfolios will be checked for rebalancing.
        force_rebalance: If True, rebalance even on weekends. Default is False.

    Returns:
        Dictionary containing rebalance operation result
    """
    rebalance_timestamp = datetime.now(ZoneInfo(DEFAULT_TIMEZONE))

    # Check if today is a weekend
    weekend_skip = skip_if_weekend(rebalance_timestamp, "manual portfolio rebalance", force=force_rebalance)
    if weekend_skip:
        # Add service-specific fields to the standard weekend skip response
        weekend_skip.update({
            "portfolios_checked": 0,
            "portfolios_rebalanced": 0,
            "portfolios_failed": 0
        })
        return weekend_skip

    try:
        # If portfolio_id is None, check all portfolios for rebalancing
        if portfolio_id is None:
            logger.info("Manual trigger: Running rebalance check for all portfolios")
            return await check_portfolios_for_rebalancing(force_rebalance)

        # Get the specific portfolio
        portfolio = await PortfolioRepository.get_portfolio(portfolio_id)

        if not portfolio:
            logger.warning(f"Portfolio with ID {portfolio_id} not found")
            return {
                "success": False,
                "message": f"Portfolio with ID {portfolio_id} not found",
                "error_code": "PORTFOLIO_NOT_FOUND",
                "timestamp": rebalance_timestamp.isoformat()
            }

        # Rebalance the portfolio
        result = await rebalance_portfolio(portfolio)

        return {
            "success": result.get("success", False),
            "message": result.get("message", "Unknown result"),
            "portfolio_id": portfolio_id,
            "rebalance_details": result,
            "timestamp": rebalance_timestamp.isoformat()
        }

    except Exception as e:
        logger.error(f"Error in manual portfolio rebalance: {str(e)}")
        return {
            "success": False,
            "message": f"Portfolio rebalance process failed: {str(e)}",
            "error_code": "MANUAL_REBALANCE_FAILED",
            "portfolio_id": portfolio_id,
            "timestamp": rebalance_timestamp.isoformat()
        }

"""
Middleware for Clerk authentication.
"""

import os
import re
from typing import Optional, List, TypedDict, Protocol, Any, cast, Dict
from clerk_backend_api import Clerk
from clerk_backend_api.jwks_helpers import AuthenticateRequestOptions, RequestState
from fastapi import Request, Response
from starlette.middleware.base import BaseHTTPMiddleware, RequestResponseEndpoint
from starlette.status import HTTP_401_UNAUTHORIZED
from starlette.types import ASGIApp


class ClerkSession(TypedDict):
    """Type definition for Clerk session data."""
    id: str
    user_id: str


class ClerkRequestState(TypedDict, total=False):
    """TypedDict for Clerk authentication request state attributes."""
    is_signed_in: bool
    clerk_auth: Optional[RequestState]
    user_id: Optional[str]
    session_id: Optional[str]


class ClerkRequest(Protocol):
    """Protocol for a Request with Clerk authentication state attributes."""
    @property
    def state(self) -> ClerkRequestState:
        """
        Get the request state.

        Returns:
            The request state.
        """
        ...


class ClerkMiddleware(BaseHTTPMiddleware):
    """
    FastAPI middleware class for Clerk authentication.

    This middleware authenticates requests using Clerk, but only for specified protected paths.
    Supports both inclusion and exclusion patterns for fine-grained control.
    """

    def __init__(
        self,
        app: ASGIApp,
        secret_key: Optional[str] = None,
        authorized_parties: Optional[List[str]] = None,
        protected_paths: Optional[List[str]] = None,
        enforce_authentication: bool = True
    ):
        """
        Initialize the Clerk middleware.

        Args:
            app: The ASGI application
            secret_key: The Clerk secret key. If not provided,
            will be read from environment variable CLERK_SECRET_KEY

            authorized_parties: List of authorized parties. Defaults to app domains
            protected_paths: List of paths to protect with authentication.
            Supports wildcards with * (e.g., "/api/*")
                            and exclusion patterns with ! prefix (e.g., "!/api/webhook/*")
            enforce_authentication: If True, will raise 401 error for
            unauthenticated requests to protected paths
        """
        super().__init__(app)
        self.secret_key = secret_key or os.getenv('CLERK_SECRET_KEY')
        if not self.secret_key:
            raise ValueError('Clerk secret key is required. '
                            'Set CLERK_SECRET_KEY environment variable or pass it as a parameter.')

        self.authorized_parties = authorized_parties or [
            'https://cse-portfolio-builder.vercel.app',
            'http://localhost:3000'
            ]
        self.protected_paths = protected_paths or ["/api/*"]  # Default to protecting /api routes
        self.enforce_authentication = enforce_authentication
        self.clerk_sdk = Clerk(bearer_auth=self.secret_key)

        # Split paths into inclusion and exclusion patterns
        self.include_patterns = []
        self.exclude_patterns = []

        for path in self.protected_paths:
            if path.startswith('!'):
                # This is an exclusion pattern
                pattern = self._compile_pattern(path[1:])  # Remove the ! prefix
                self.exclude_patterns.append(pattern)
            else:
                # This is an inclusion pattern
                pattern = self._compile_pattern(path)
                self.include_patterns.append(pattern)

    def _compile_pattern(self, path: str) -> re.Pattern:
        """
        Compile a path pattern into a regex pattern.

        Args:
            path: The path pattern to compile

        Returns:
            A compiled regex pattern
        """
        # Convert glob-style pattern to regex
        # Replace * with regex pattern for any characters
        pattern = path.replace('*', '.*')
        # Ensure the pattern matches from the start of the path
        if not pattern.startswith('^'):
            pattern = '^' + pattern
        # Compile the regex pattern
        return re.compile(pattern)

    def _is_protected_path(self, path: str) -> bool:
        """
        Check if the path should be protected with authentication.

        Args:
            path: The request path

        Returns:
            True if the path should be protected, False otherwise
        """
        # First check exact matches for inclusion
        if path in [p for p in self.protected_paths if not p.startswith('!')]:
            # Then check for exact exclusion matches
            if path in [p[1:] for p in self.protected_paths if p.startswith('!')]:
                return False
            return True

        # Check pattern matches
        is_included = False

        # First check if path matches any inclusion pattern
        for pattern in self.include_patterns:
            if pattern.match(path):
                is_included = True
                break

        # If not included by any pattern, it's not protected
        if not is_included:
            return False

        # If included, check if it's explicitly excluded
        for pattern in self.exclude_patterns:
            if pattern.match(path):
                return False  # Path is explicitly excluded

        # Path is included and not excluded
        return True

    def _initialize_request_state(self, request: Request) -> None:
        """
        Initialize the request state with default authentication values.

        Args:
            request: The incoming request
        """
        # Cast to Any to allow setting attributes on state (typing workaround)
        state = cast(Any, request.state)

        # Initialize default state (unauthenticated)
        state.is_signed_in = False
        state.clerk_auth = None
        state.user_id = None
        state.session_id = None

    async def dispatch(
        self, request: Request, call_next: RequestResponseEndpoint
    ) -> Response:
        """
        Dispatch the request and authenticate it if it's a protected path.

        Args:
            request: The incoming request
            call_next: The next middleware or endpoint to call

        Returns:
            The response from the next middleware or endpoint
        """
        # Initialize request state with default values
        self._initialize_request_state(request)

        # Skip authentication for OPTIONS requests to allow CORS preflight
        if request.method == "OPTIONS":
            return await call_next(request)

        # Check if this is a path that needs protection
        path = request.url.path
        needs_protection = self._is_protected_path(path)

        # If path doesn't need protection, skip authentication
        if not needs_protection:
            return await call_next(request)

        # For protected paths, perform authentication
        try:
            # Authenticate the request using Clerk
            request_state = self.clerk_sdk.authenticate_request(
                request,  # type: ignore
                AuthenticateRequestOptions(
                    authorized_parties=self.authorized_parties
                )
            )

            # Cast to Any to allow setting attributes on state (typing workaround)
            state = cast(Any, request.state)

            # Add authentication data to request state
            state.clerk_auth = request_state
            state.is_signed_in = request_state.is_signed_in

            # If there's a session, add user data to request state
            session = getattr(request_state, 'session', None)
            if session and isinstance(session, Dict):
                state.user_id = session.get('user_id')
                state.session_id = session.get('id')

            # If enforcing authentication and user is not signed in, return 401
            if self.enforce_authentication and not request_state.is_signed_in:
                return Response(
                    content='{"detail":"Authentication required"}',
                    status_code=HTTP_401_UNAUTHORIZED,
                    media_type="application/json"
                )

        except Exception as e:
            # Handle authentication errors
            if self.enforce_authentication:
                return Response(
                    content=f'{{"detail":"Authentication error: {str(e)}"}}',
                    status_code=HTTP_401_UNAUTHORIZED,
                    media_type="application/json"
                )

        # Continue with the request
        response = await call_next(request)
        return response


# Type helper for endpoints to get properly typed requests
def get_clerk_request(request: Request) -> ClerkRequest:
    """
    Cast a regular request to a ClerkRequest to get proper typing for Clerk state attributes.

    Usage:
        @app.get("/api/protected")
        async def protected_endpoint(request: Request):
            clerk_request = get_clerk_request(request)
            user_id = clerk_request.state["user_id"]  # Properly typed

    Args:
        request: The FastAPI request object

    Returns:
        The same request but cast to ClerkRequest for better typing
    """
    return cast(ClerkRequest, request)

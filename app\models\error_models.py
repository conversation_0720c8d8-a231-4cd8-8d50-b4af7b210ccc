"""
Error Models

This module defines standardized error response models for the API.
"""

from enum import Enum
from typing import Optional, Dict, Any, List
from pydantic import BaseModel, Field

class ErrorTypes(str, Enum):
    """Enumeration of possible error types."""
    UNAUTHORIZED = "unauthorized"
    FORBIDDEN = "forbidden"
    NOT_FOUND = "not_found"
    VALIDATION_ERROR = "validation_error"
    SERVER_ERROR = "server_error"
    MISSING_COMPANIES = "missing_companies"
    INSUFFICIENT_COMPANIES = "insufficient_companies"
    INVALID_PORTFOLIO_TYPE = "invalid_portfolio_type"
    INEFFICIENT_TARGET_RETURN = "inefficient_target_return"
    UNACHIEVABLE_TARGET_RETURN = "unachievable_target_return"
    MISSING_DATE_RANGE = "missing_date_range"
    INVALID_RISK_FREE_RATE = "invalid_risk_free_rate"
    INVALID_CONSTRAINT_SET = "invalid_constraint_set"
    INVALID_REBALANCE_FREQUENCY = "invalid_rebalance_frequency"
    INVALID_INVESTMENT_AMOUNT = "invalid_investment_amount"
    INVALID_PORTFOLIO_NAME = "invalid_portfolio_name"
    REBALANCE_NOT_NEEDED = "rebalance_not_needed"
    PORTFOLIO_NOT_FOUND = "portfolio_not_found"
    BENCHMARK_DATA_NOT_FOUND = "benchmark_data_not_found"
    PERFORMANCE_HISTORY_NOT_FOUND = "performance_history_not_found"
    UPDATE_FAILED = "update_failed"
    REBALANCE_FAILED = "rebalance_failed"
    REBALANCE_CHECK_FAILED = "rebalance_check_failed"
    DELETION_FAILED = "deletion_failed"
    INVALID_PORTFOLIO_ID = "invalid_portfolio_id"

class ErrorDetail(BaseModel):
    """Detailed error information."""
    issue: ErrorTypes = Field(..., description="Type of error that occurred")
    description: str = Field(..., description="Human-readable description of the error")
    fix: str = Field(..., description="Suggested fix or action to resolve the error")
    explanation: Optional[str] = Field(None, description="Additional explanation of why the error occurred")
    recommendation: Optional[str] = Field(None, description="Specific recommendation to resolve the error")
    suggestion: Optional[str] = Field(None, description="Alternative suggestion or approach")
    reference_return: Optional[float] = Field(None, description="Reference return value for target return errors")
    target_return: Optional[float] = Field(None, description="Target return value that caused the error")
    affected_fields: Optional[List[str]] = Field(None, description="List of fields affected by the error")
    constraints: Optional[Dict[str, Any]] = Field(None, description="Constraints that caused the error")

class APIErrorResponse(BaseModel):
    """Standardized API error response."""
    success: bool = Field(False, description="Always false for error responses")
    error: ErrorDetail = Field(..., description="Detailed error information")
    status_code: int = Field(..., description="HTTP status code for the error")

def create_error_response(
    issue: ErrorTypes,
    description: str,
    fix: str,
    status_code: int,
    explanation: Optional[str] = None,
    recommendation: Optional[str] = None,
    suggestion: Optional[str] = None,
    reference_return: Optional[float] = None,
    target_return: Optional[float] = None,
    affected_fields: Optional[List[str]] = None,
    constraints: Optional[Dict[str, Any]] = None
) -> Dict[str, Any]:
    """
    Create a standardized error response.

    Args:
        issue: Type of error that occurred
        description: Human-readable description of the error
        fix: Suggested fix or action to resolve the error
        status_code: HTTP status code for the error
        explanation: Optional additional explanation of why the error occurred
        recommendation: Optional specific recommendation to resolve the error
        suggestion: Optional alternative suggestion or approach
        reference_return: Optional reference return value for target return errors
        target_return: Optional target return value that caused the error
        affected_fields: Optional list of fields affected by the error
        constraints: Optional constraints that caused the error

    Returns:
        Dictionary containing the standardized error response
    """
    error_detail = ErrorDetail(
        issue=issue,
        description=description,
        fix=fix,
        explanation=explanation,
        recommendation=recommendation,
        suggestion=suggestion,
        reference_return=reference_return,
        target_return=target_return,
        affected_fields=affected_fields,
        constraints=constraints
    )

    return APIErrorResponse(
        success=False,
        error=error_detail,
        status_code=status_code
    ).model_dump()

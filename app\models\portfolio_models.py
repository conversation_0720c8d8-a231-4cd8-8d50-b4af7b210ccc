"""
Portfolio Models

This module defines the data models for portfolio-related API requests and responses.
"""

from typing import Dict, List, Optional, Any
from datetime import datetime
from pydantic import BaseModel, Field, field_validator
from app.config import PORTFOLIO_TYPE_CUSTOM


class PortfolioRequest(BaseModel):
    """
    Request model for portfolio optimization

    Attributes:
        companies: Dictionary mapping company names to their ticker symbols
        start_date: Start date for historical data analysis in YYYY-MM-DD format
        end_date: Optional end date for historical data analysis in YYYY-MM-DD format
        (defaults to current date)

        risk_free_rate: Risk-free rate used in
        Sharpe ratio calculations (as decimal, e.g., 0.02 for 2%)

        constraint_set: Optional constraints for portfolio weights as (min_weight, max_weight)
    """
    companies: Dict[str, str] = Field(
        ..., description='''Dictionary of company names and their
        ticker symbols (e.g., {'Apple': 'AAPL'})''')
    from_date: str = Field("2015-01-01", description='''Start date for
                            historical data analysis (YYYY-MM-DD)''')
    to_date: Optional[str] = Field(None, description='''End date for analysis (YYYY-MM-DD),
                                    defaults to current date if not provided''')
    risk_free_rate: float = Field(0.0,
                            description='''Risk-free rate for Sharpe ratio calculations
                            (decimal format, e.g., 0.02 for 2%)''')
    constraint_set: Optional[List[float]] = Field([0, 1],
                            description='''Constraints for portfolio weights as
                            [min_weight, max_weight]''')

class PortfolioPerformance(BaseModel):
    """Performance metrics for a portfolio."""
    returns: float = Field(..., description="Annualized expected return percentage")
    volatility: float = Field(..., description="Annualized volatility (standard deviation) percentage")
    sharpe_ratio: float = Field(..., description="Sharpe ratio (risk-adjusted return)")

class PortfolioAllocation(BaseModel):
    """Portfolio allocation with performance metrics."""
    performance: PortfolioPerformance
    allocations: Dict[str, float] = Field(
        ...,
        description="Dictionary mapping ticker symbols to allocation percentages"
    )

class EfficientFrontierPoint(BaseModel):
    """A point on the efficient frontier."""
    volatility: float = Field(..., description="Volatility (standard deviation) percentage")
    returns: float = Field(..., description="Expected return percentage")

class PortfolioConstraints(BaseModel):
    """Constraints applied to the portfolio optimization."""
    weight_bounds: List[float] = Field(..., description="Min and max weight constraints [min, max]")

class PortfolioAnalysisResponse(BaseModel):
    """Response model for portfolio analysis."""
    max_sharpe_ratio: PortfolioAllocation
    min_variance: PortfolioAllocation
    efficient_frontier: List[EfficientFrontierPoint]
    constraints: PortfolioConstraints

class PortfolioCreationRequest(BaseModel):
    """Request model for portfolio creation."""
    portfolio_type: str = Field(
        ...,
        description="Type of portfolio to create: 'min_variance', 'max_sharpe', or 'custom'"
    )
    portfolio_name: str = Field(
        ...,
        description="Name for the portfolio"
    )
    investment_amount: float = Field(
        ...,
        description="Total investment amount"
    )
    custom_companies: Optional[Dict[str, str]] = Field(
        None,
        description="Dictionary mapping company names to ticker symbols (for custom portfolios)"
    )
    excluded_companies: Optional[Dict[str, str]] = Field(
        None,
        description="Dictionary mapping company names to ticker symbols to exclude from S&PSL20 (for min_variance and max_sharpe portfolios)"
    )
    selected_industry_groups: Optional[List[str]] = Field(
        None,
        description="List of selected industry groups for custom portfolio creation"
    )
    selected_companies: Optional[Dict[str, List[str]]] = Field(
        None,
        description="Dictionary mapping industry groups to lists of selected company names"
    )
    from_date: Optional[str] = Field(
        None,
        description="Start date for historical data analysis in YYYY-MM-DD format"
    )
    to_date: Optional[str] = Field(
        None,
        description="End date for historical data analysis in YYYY-MM-DD format"
    )
    risk_free_rate: Optional[float] = Field(
        None,
        description="Risk-free rate used for Sharpe ratio calculation (e.g., 0.08 for 8%)"
    )
    constraint_set: Optional[List[float]] = Field(
        None,
        description="Min and max weight constraints for all assets [min, max]"
    )
    target_return: Optional[float] = Field(
        None,
        description="Target return for custom portfolios as decimal (e.g., 0.20 for 20%)"
    )
    rebalance_frequency: str = Field(
        "ANNUALLY",
        description="How often to rebalance the portfolio (MONTHLY, QUARTERLY, SEMI-ANNUALLY, ANNUALLY)"
    )

    @field_validator('rebalance_frequency')
    @classmethod
    def validate_rebalance_frequency(cls, v):
        """Validate that rebalance frequency is one of the allowed values."""
        allowed_values = ["MONTHLY", "QUARTERLY", "SEMI-ANNUALLY", "ANNUALLY"]
        if v and v not in allowed_values:
            raise ValueError(f"Rebalance frequency must be one of: {', '.join(allowed_values)}")
        return v

    @field_validator('target_return')
    @classmethod
    def validate_target_return_for_custom(cls, v, info):
        """Validate that target return is provided for custom portfolios and within reasonable limits."""
        if info.data.get('portfolio_type') == PORTFOLIO_TYPE_CUSTOM and v is None:
            raise ValueError("Target return is required for custom portfolios")

        if v is not None:
            # Check if target return is within reasonable limits
            if v < 0:
                raise ValueError("Target return cannot be negative")
            # Removed static 100% limit - let the efficient frontier validation handle maximum limits
            # based on actual market data and selected companies

        return v

    @field_validator('from_date', 'to_date', 'risk_free_rate', 'constraint_set', 'custom_companies')
    @classmethod
    def validate_required_fields_for_custom(cls, v, info):
        """Validate that required fields are provided for custom portfolios."""
        field_name = info.field_name

        # Only validate for custom portfolios
        if info.data.get('portfolio_type') == PORTFOLIO_TYPE_CUSTOM and v is None:
            raise ValueError(f"{field_name} is required for custom portfolios")

        # Special validation for custom_companies
        if field_name == 'custom_companies' and info.data.get('portfolio_type') == PORTFOLIO_TYPE_CUSTOM:
            if not v or len(v) < 5:
                raise ValueError("At least 5 companies must be selected for custom portfolios")

        return v

    @field_validator('investment_amount')
    @classmethod
    def validate_investment_amount(cls, v):
        """Validate that investment amount is positive and within reasonable limits."""
        if v <= 0:
            raise ValueError("Investment amount must be greater than zero")
        if v > 1000000000:  # 1 billion LKR limit
            raise ValueError("Investment amount exceeds the maximum allowed (1 billion LKR)")
        return v

    @field_validator('portfolio_name')
    @classmethod
    def validate_portfolio_name(cls, v):
        """Validate that portfolio name is not empty."""
        if not v or len(v.strip()) == 0:
            raise ValueError("Portfolio name cannot be empty")
        return v

    @field_validator('constraint_set')
    @classmethod
    def validate_constraint_set(cls, v):
        """Validate that weight constraints are valid."""
        if v is not None:
            if len(v) != 2:
                raise ValueError("Weight constraints must be a list of two values [min, max]")

            min_weight, max_weight = v

            if min_weight < 0:
                raise ValueError("Minimum weight cannot be negative")

            if max_weight > 1:
                raise ValueError("Maximum weight cannot exceed 1.0 (100%)")

            if min_weight > max_weight:
                raise ValueError("Minimum weight cannot be greater than maximum weight")

        return v

class PortfolioCreationResponse(BaseModel):
    """Response model for portfolio creation."""
    portfolio_id: Optional[str] = Field(None, description="ID of the created portfolio")
    portfolio_type: str = Field(..., description="Type of portfolio created")
    description: str = Field(..., description="Description of the portfolio")
    performance: PortfolioPerformance
    allocations: Dict[str, float] = Field(
        ...,
        description="Dictionary mapping ticker symbols to allocation percentages"
    )
    constraints_used: Dict[str, Any] = Field(
        ...,
        description="Constraints used for portfolio optimization"
    )
    message: Optional[str] = Field(None, description="Message about the portfolio creation")

class PortfolioHolding(BaseModel):
    """Model for a portfolio holding."""
    company_name: str = Field(..., description="Company name")
    ticker_symbol: str = Field(..., description="Ticker symbol")
    quantity: int = Field(..., description="Number of shares (integer, no fractional shares)")
    last_traded_price: float = Field(..., description="Latest close price in LKR")
    total_cost: float = Field(..., description="Initial investment in this holding in LKR")
    market_value: float = Field(..., description="Current market value in LKR")
    unrealized_gain_loss: float = Field(..., description="Market value - total cost in LKR")
    weight: float = Field(..., description="Allocation weight (0-1)")
    annualized_return: float = Field(..., description="Individual asset's annualized return")
    annualized_volatility: float = Field(..., description="Individual asset's annualized volatility")

class SavedPortfolio(BaseModel):
    """Model for a saved portfolio."""
    portfolio_id: str = Field(..., description="Portfolio ID")
    portfolio_name: str = Field(..., description="User-provided name")
    portfolio_type: str = Field(..., description="Type of portfolio (MIN_VARIANCE, MAX_SHARPE, CUSTOM)")
    total_investment_amount: float = Field(..., description="Initial investment amount in LKR")
    total_market_value: float = Field(..., description="Current market value in LKR")
    unrealized_gain_loss: float = Field(..., description="Market value - investment amount in LKR")
    annualized_return: float = Field(..., description="Portfolio's annualized return (percentage)")
    annualized_volatility: float = Field(..., description="Portfolio's annualized volatility (percentage)")
    creation_date: datetime = Field(..., description="Date when portfolio was created")
    rebalance_frequency: str = Field(..., description="How often to rebalance the portfolio")
    last_rebalanced_date: datetime = Field(..., description="Date of last rebalance")
    last_updated_date: datetime = Field(..., description="Date when portfolio was last updated with market data")
    holdings: List[PortfolioHolding] = Field(..., description="Portfolio holdings")
    cash_component: Optional[float] = Field(0.0, description="Unallocated cash in LKR")

class PortfolioListItem(BaseModel):
    """Model for a portfolio list item."""
    portfolio_id: str = Field(..., description="Portfolio ID")
    portfolio_name: str = Field(..., description="User-provided name")
    portfolio_type: str = Field(..., description="Type of portfolio (MIN_VARIANCE, MAX_SHARPE, CUSTOM)")
    total_investment_amount: float = Field(..., description="Initial investment amount in LKR")
    total_market_value: float = Field(..., description="Current market value in LKR")
    unrealized_gain_loss: float = Field(..., description="Market value - investment amount in LKR")
    annualized_return: float = Field(..., description="Portfolio's annualized return (percentage)")
    annualized_volatility: float = Field(..., description="Portfolio's annualized volatility (percentage)")
    creation_date: datetime = Field(..., description="Date when portfolio was created")
    rebalance_frequency: str = Field(..., description="How often to rebalance the portfolio")
    last_rebalanced_date: datetime = Field(..., description="Date of last rebalance")
    last_updated_date: datetime = Field(..., description="Date when portfolio was last updated with market data")
    holdings_count: int = Field(..., description="Number of holdings in the portfolio")
    cash_component: Optional[float] = Field(0.0, description="Unallocated cash in LKR")

class PortfolioListResponse(BaseModel):
    """Response model for portfolio list."""
    portfolios: List[PortfolioListItem] = Field(..., description="List of portfolios")

class PortfolioResponse(BaseModel):
    """Response model for portfolio details."""
    portfolio: SavedPortfolio = Field(..., description="Portfolio details")

class SectorAllocation(BaseModel):
    """Model for a sector allocation."""
    sector: str = Field(..., description="Sector name")
    allocation_percentage: float = Field(..., description="Percentage of portfolio allocated to this sector")
    market_value: float = Field(..., description="Market value allocated to this sector in LKR")

class SectorAllocationsResponse(BaseModel):
    """Response model for portfolio sector allocations."""
    portfolio_id: str = Field(..., description="Portfolio ID")
    portfolio_name: str = Field(..., description="Portfolio name")
    total_market_value: float = Field(..., description="Total market value of the portfolio in LKR")
    sector_allocations: List[SectorAllocation] = Field(..., description="List of sector allocations")

class PortfolioConstraintsResponse(BaseModel):
    """Response model for default portfolio constraints."""
    start_date: str = Field(..., description="Default start date for historical data")
    end_date: str = Field(..., description="Default end date for historical data")
    risk_free_rate: float = Field(..., description="Default risk-free rate")
    weight_bounds: List[float] = Field(..., description="Default weight bounds [min, max]")
    default_companies: Dict[str, str] = Field(..., description="Default companies to use (S&PSL20)")

class PerformanceDataPoint(BaseModel):
    """Model for a single data point in the portfolio performance history."""
    date: str = Field(..., description="Date in YYYY-MM-DD format")
    market_value: float = Field(..., description="Total market value of the portfolio on this date")

class PortfolioPerformanceHistoryResponse(BaseModel):
    """Response model for portfolio performance history."""
    portfolio_id: str = Field(..., description="Portfolio ID")
    portfolio_name: str = Field(..., description="Portfolio name")
    initial_investment: float = Field(..., description="Initial investment amount in LKR")
    performance_data: List[PerformanceDataPoint] = Field(..., description="List of performance data points")
    date_range: Dict[str, str] = Field(..., description="Date range for the performance data")

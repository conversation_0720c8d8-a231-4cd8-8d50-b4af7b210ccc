"""
Portfolio Storage Service

This module provides business logic for storing and retrieving portfolios with:
- Integer share quantities (no fractional shares)
- Market value equal to investment amount at creation
- Proper handling of currency (LKR)
"""

import logging
import math
from datetime import datetime
from typing import Dict, List, Optional, Any, Tuple
from decimal import Decimal
from bson.decimal128 import Decimal128

from app.db.repositories import TradeRepository, CompanyRepository, PortfolioRepository

logger = logging.getLogger(__name__)

def safe_decimal128(value, default='0', context_name=None):
    """
    Convert a value to Decimal128 safely with improved error handling.

    Args:
        value: The value to convert to Decimal128
        default: The default value to use if conversion fails (default: '0')
        context_name: Optional name for logging context (e.g., field name)

    Returns:
        Decimal128 representation of the value, or default if conversion fails
    """
    if value is None:
        return None

    try:
        # Ensure we're working with a string to avoid float precision issues
        decimal_value = Decimal(str(value))

        # Round to 4 decimal places for consistency
        decimal_value = decimal_value.quantize(Decimal('0.0001'))

        return Decimal128(str(decimal_value))
    except Exception as e:
        context = f" for {context_name}" if context_name else ""
        logger.warning(f"Failed to convert {value} (type: {type(value)}) to Decimal128{context}: {str(e)}")

        # Return the default value as Decimal128
        try:
            return Decimal128(default)
        except Exception as e:
            logger.error(f"Default value '{default}' is also invalid for Decimal128, using '0'")
            return Decimal128('0')

async def _fetch_latest_trades(tickers: List[str]) -> Dict[str, Dict[str, Any]]:
    """
    Fetch latest trade data for a list of tickers.

    Args:
        tickers: List of ticker symbols

    Returns:
        Dictionary mapping ticker symbols to their latest trade data
    """
    latest_trades = {}
    for ticker in tickers:
        latest_trade = await TradeRepository.get_latest_trade(ticker)
        if latest_trade:
            latest_trades[ticker] = latest_trade
        else:
            logger.warning(f"No trade data found for {ticker}")

    return latest_trades

async def _calculate_allocations(
    allocations: Dict[str, float],
    latest_trades: Dict[str, Dict[str, Any]],
    investment_amount: float
) -> Dict[str, Dict[str, Any]]:
    """
    Calculate integer share quantities and adjusted allocations.

    Args:
        allocations: Dictionary mapping ticker symbols to allocation weights
        latest_trades: Dictionary mapping ticker symbols to their latest trade data
        investment_amount: Total investment amount

    Returns:
        Dictionary mapping ticker symbols to their adjusted allocation data
    """
    adjusted_allocations = {}
    total_allocated = 0

    # First pass: Calculate quantities as integers and adjust weights if needed
    for ticker, weight in allocations.items():
        # Skip if no trade data
        if ticker not in latest_trades:
            continue

        # Get the latest trade data
        latest_trade = latest_trades[ticker]

        # Calculate holding details
        last_traded_price = float(latest_trade.get("close", 0))
        if last_traded_price <= 0:
            logger.warning(f"Invalid price ({last_traded_price}) for {ticker}")
            continue

        # Calculate allocation amount and quantity
        allocation_amount = investment_amount * weight

        # Calculate quantity as an integer (round down to ensure we don't exceed budget)
        quantity = math.floor(allocation_amount / last_traded_price)

        # Recalculate the actual allocation amount based on integer quantity
        actual_allocation = quantity * last_traded_price

        # Store the adjusted allocation for later use
        adjusted_allocations[ticker] = {
            "weight": weight,
            "quantity": quantity,
            "price": last_traded_price,
            "allocation": actual_allocation
        }

        # Track total allocated amount
        total_allocated += actual_allocation

    # Calculate remaining amount to allocate
    remaining = investment_amount - total_allocated
    logger.info(f"Initial allocation: {total_allocated:.2f} LKR, Remaining: {remaining:.2f} LKR")

    # Second pass: Distribute remaining amount to stocks with highest weights
    if remaining > 0 and adjusted_allocations:
        # Sort stocks by weight in descending order
        sorted_stocks = sorted(adjusted_allocations.items(), key=lambda x: x[1]["weight"], reverse=True)

        for ticker, data in sorted_stocks:
            price = data["price"]

            # If we can buy at least one more share
            if remaining >= price:
                # Buy one more share
                adjusted_allocations[ticker]["quantity"] += 1
                adjusted_allocations[ticker]["allocation"] += price
                remaining -= price

                logger.debug(f"Added 1 more share of {ticker} at {price:.2f} LKR, Remaining: {remaining:.2f} LKR")

                # If remaining amount is too small, break
                if remaining < min(adjusted_allocations.values(), key=lambda x: x["price"])["price"]:
                    break

    return adjusted_allocations

async def _fetch_company_info_and_performance(active_tickers: List[str]) -> Tuple[Dict[str, Dict[str, Any]], Dict[str, Dict[str, float]]]:
    """
    Fetch company information and historical performance for a list of tickers.

    Args:
        active_tickers: List of ticker symbols with non-zero quantities

    Returns:
        Tuple of (company_infos, asset_performances)
    """
    company_infos = {}
    asset_performances = {}

    for ticker in active_tickers:
        company_info = await CompanyRepository.get_company_by_symbol(ticker)
        if company_info:
            company_infos[ticker] = company_info

        # Get historical performance for this asset
        asset_performance = await get_asset_performance(ticker)
        asset_performances[ticker] = asset_performance

    return company_infos, asset_performances

async def save_portfolio(
    portfolio_type: str,
    portfolio_name: str,
    investment_amount: float,
    allocations: Dict[str, float],
    performance: Dict[str, float],
    rebalance_frequency: str = "ANNUALLY",
    constraints: Dict[str, Any] = None
) -> Optional[str]:
    """
    Save a portfolio to the database with integer share quantities.

    Args:
        portfolio_type: Type of portfolio (MIN_VARIANCE, MAX_SHARPE, CUSTOM)
        portfolio_name: User-provided name for the portfolio
        investment_amount: Total investment amount in LKR
        allocations: Dictionary mapping ticker symbols to allocation weights (0-1)
        performance: Dictionary containing performance metrics
        rebalance_frequency: How often to rebalance the portfolio
        constraints: Constraints used to create the portfolio

    Returns:
        Portfolio ID if successful, None otherwise
    """
    try:
        # Validate allocations
        if not allocations:
            raise ValueError("No allocations provided for portfolio creation")

        # Validate that the sum of weights is close to 1.0
        total_weight = sum(allocations.values())
        if abs(total_weight - 1.0) > 0.01:  # Allow 1% tolerance
            logger.warning(f"Sum of allocation weights ({total_weight:.4f}) is not close to 1.0. Normalizing weights.")
            # Normalize weights to ensure they sum to 1.0
            allocations = {ticker: weight / total_weight for ticker, weight in allocations.items()}

        # Prepare holdings data
        holdings = []
        total_market_value = 0

        logger.debug(f"Processing {len(allocations)} allocations")

        # Get all tickers
        tickers = list(allocations.keys())

        # Fetch latest trade data for all tickers
        latest_trades = await _fetch_latest_trades(tickers)

        # Calculate allocations with integer quantities
        adjusted_allocations = await _calculate_allocations(allocations, latest_trades, investment_amount)

        # Get active tickers (with non-zero quantities)
        active_tickers = [ticker for ticker, data in adjusted_allocations.items() if data["quantity"] > 0]

        # Fetch company information and performance data
        company_infos, asset_performances = await _fetch_company_info_and_performance(active_tickers)

        # Create holdings with adjusted quantities and weights
        for ticker, data in adjusted_allocations.items():
            quantity = data["quantity"]
            last_traded_price = data["price"]
            market_value = quantity * last_traded_price

            # Skip if quantity is zero
            if quantity <= 0:
                continue

            # Recalculate weight based on actual market value
            actual_weight = market_value / investment_amount

            # Get company name from cached data
            company_info = company_infos.get(ticker, {})
            company_name = company_info.get("COMPANY_NAME", ticker) if company_info else ticker

            # Get performance from cached data
            asset_performance = asset_performances.get(ticker, {"return": 0, "volatility": 0})

            holding = {
                "companyName": company_name,
                "tickerSymbol": ticker,
                "quantity": safe_decimal128(quantity, context_name=f"{ticker}_quantity"),  # Integer quantity
                "lastTradedPrice": safe_decimal128(last_traded_price, context_name=f"{ticker}_price"),
                "totalCost": safe_decimal128(market_value, context_name=f"{ticker}_cost"),  # Based on integer quantity
                "marketValue": safe_decimal128(market_value, context_name=f"{ticker}_market_value"),  # Equal to totalCost at creation
                "unrealizedGainLoss": safe_decimal128(0, context_name=f"{ticker}_unrealized_gain_loss"),  # Zero at creation
                "weight": safe_decimal128(actual_weight, context_name=f"{ticker}_weight"),  # Recalculated weight
                "annualizedReturn": safe_decimal128(asset_performance.get("return", 0), context_name=f"{ticker}_return"),
                "annualizedVolatility": safe_decimal128(asset_performance.get("volatility", 0), context_name=f"{ticker}_volatility")
            }

            holdings.append(holding)
            total_market_value += market_value

        # Ensure we have holdings
        if not holdings:
            logger.error(f"No valid holdings could be created for portfolio {portfolio_name}")
            return None

        # Calculate cash component (unallocated funds)
        cash_component = investment_amount - total_market_value

        # Create portfolio data
        current_time = datetime.now()
        portfolio_data = {
            "portfolioName": portfolio_name,
            "portfolioType": portfolio_type,
            "totalInvestmentAmount": safe_decimal128(investment_amount, context_name="total_investment"),
            "totalMarketValue": safe_decimal128(total_market_value, context_name="total_market_value"),  # Should be close to investment_amount
            "totalUnrealizedGainLoss": safe_decimal128(0, context_name="total_unrealized_gain_loss"),  # Zero at creation
            "annualizedReturn": safe_decimal128(performance.get("returns", 0), context_name="portfolio_return"),
            "annualizedVolatility": safe_decimal128(performance.get("volatility", 0), context_name="portfolio_volatility"),
            "rebalanceFrequency": rebalance_frequency,
            "holdings": holdings,
            "constraints": constraints,
            "creationDate": current_time,
            "lastRebalancedDate": current_time,  # Initialize lastRebalancedDate to creation time
            "lastUpdatedDate": current_time,  # Initialize lastUpdatedDate to creation time
            "isActive": True,  # Portfolio is active by default
            "cashComponent": safe_decimal128(cash_component, context_name="cash_component")  # Store unallocated funds
        }

        # Log summary
        logger.info(f"Creating portfolio with {len(holdings)} holdings")
        logger.info(f"Total investment amount: {investment_amount:,.2f} LKR")
        logger.info(f"Total market value: {total_market_value:,.2f} LKR")
        logger.info(f"Difference: {investment_amount - total_market_value:,.2f} LKR ({(investment_amount - total_market_value) / investment_amount * 100:.2f}%)")

        # Save the portfolio
        portfolio_id = await PortfolioRepository.create_portfolio(portfolio_data)

        return portfolio_id

    except Exception as e:
        logger.error(f"Error saving portfolio: {str(e)}")
        return None

async def get_asset_performance(ticker: str) -> Dict[str, float]:
    """
    Get historical performance metrics for an asset.

    Args:
        ticker: Ticker symbol

    Returns:
        Dictionary with return and volatility
    """
    try:
        # Get historical data for the past year
        end_date = datetime.now()
        start_date = datetime(end_date.year - 1, end_date.month, end_date.day)

        trades = await TradeRepository.get_historical_trades(ticker, start_date, end_date)

        if not trades or len(trades) < 30:  # Need sufficient data
            return {"return": 0, "volatility": 0}

        # Calculate annualized return and volatility
        import pandas as pd
        import numpy as np

        # Extract dates and prices
        dates = [trade["tradeDate"] for trade in trades]
        prices = [float(trade["close"]) for trade in trades]

        # Create Series and calculate returns
        price_series = pd.Series(prices, index=dates)
        returns = price_series.pct_change().dropna()

        # Calculate annualized metrics
        ann_return = returns.mean() * 252  # 252 trading days in a year
        ann_volatility = returns.std() * np.sqrt(252)

        return {
            "return": float(ann_return),
            "volatility": float(ann_volatility)
        }

    except Exception as e:
        logger.error(f"Error calculating performance for {ticker}: {str(e)}")
        return {"return": 0, "volatility": 0}

async def get_portfolio(portfolio_id: str) -> Optional[Dict[str, Any]]:
    """
    Get a portfolio by ID.

    Args:
        portfolio_id: Portfolio ID

    Returns:
        Portfolio data if found, None otherwise
    """
    try:
        portfolio = await PortfolioRepository.get_portfolio(portfolio_id)
        return portfolio
    except Exception as e:
        logger.error(f"Error getting portfolio {portfolio_id}: {str(e)}")
        return None

async def get_all_portfolios() -> List[Dict[str, Any]]:
    """
    Get all portfolios.

    Returns:
        List of portfolios
    """
    try:
        portfolios = await PortfolioRepository.get_all_portfolios()
        return portfolios
    except Exception as e:
        logger.error(f"Error getting all portfolios: {str(e)}")
        return []

async def delete_portfolio(portfolio_id: str) -> Dict[str, Any]:
    """
    Delete a portfolio by ID.

    Args:
        portfolio_id: Portfolio ID

    Returns:
        Dictionary containing the result of the operation
    """
    try:
        # First check if the portfolio exists
        portfolio = await get_portfolio(portfolio_id)
        if not portfolio:
            return {
                "success": False,
                "message": f"Portfolio with ID {portfolio_id} not found",
                "error_code": "PORTFOLIO_NOT_FOUND"
            }

        # Delete the portfolio
        try:
            await PortfolioRepository.delete_portfolio(portfolio_id)

            return {
                "success": True,
                "message": f"Portfolio {portfolio_id} deleted successfully",
                "portfolio_id": portfolio_id
            }
        except ValueError as ve:
            # Portfolio not found
            return {
                "success": False,
                "message": str(ve),
                "error_code": "PORTFOLIO_NOT_FOUND"
            }
        except RuntimeError as re:
            # Database operation failed
            logger.error(f"Database error when deleting portfolio {portfolio_id}: {str(re)}")
            return {
                "success": False,
                "message": f"Database error: {str(re)}",
                "error_code": "DATABASE_ERROR"
            }
    except Exception as e:
        logger.error(f"Error deleting portfolio {portfolio_id}: {str(e)}")
        return {
            "success": False,
            "message": f"Error deleting portfolio: {str(e)}",
            "error_code": "INTERNAL_SERVER_ERROR"
        }

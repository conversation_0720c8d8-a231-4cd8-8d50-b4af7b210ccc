'''
This module contains the models for the news data that comes from the CSE API.
'''
from typing import Dict, Optional, List
from datetime import datetime
from pydantic import BaseModel, Field, RootModel


class LocalizationPDF(BaseModel):
    """Model for PDF localization data"""
    pdf: str


class Localization(BaseModel):
    """Model for news article localization data"""
    si: Optional[LocalizationPDF] = None
    en: Optional[LocalizationPDF] = None
    ta: Optional[LocalizationPDF] = None


class NewsArticle(BaseModel):
    """Model for a single news article"""
    id: str
    title: str
    shortDescription: str
    publishedDate: datetime
    security: int = Field(default=0)
    year: int
    month: str
    image1: Optional[str] = None
    image2: Optional[str] = None
    image3: Optional[str] = None
    type: str
    source: str
    companyLogo: Optional[str] = None
    image1Orientation: str
    image2Orientation: str
    image3Orientation: str
    web: bool
    mobile: bool
    localization: Localization


class MonthlyNews(RootModel[Dict[str, List[NewsArticle]]]):
    pass
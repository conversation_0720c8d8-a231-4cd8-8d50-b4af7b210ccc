from bson.decimal128 import Decimal128
from typing import Any

def convert_decimal_to_float(value: Any) -> float:
    """
    Convert Decimal128 to float, preserving numeric types and handling None values.
    
    Args:
        value: Value to convert, can be Decimal128, numeric type, or None
        
    Returns:
        float: Converted float value or 0.0 if value is None or not convertible
    """
    if value is None:
        return 0.0
    if isinstance(value, Decimal128):
        return float(value.to_decimal())
    if isinstance(value, (int, float)):
        return float(value)
    # Try to convert other types that might be numeric representations
    try:
        return float(value)
    except (ValueError, TypeError):
        return 0.0
"""
Portfolio Optimisation Engine

This module implements Modern Portfolio Theory for portfolio optimization with enhanced
support for different constraint types and optimization strategies.
"""

import pandas as pd
import numpy as np
import scipy.optimize as sp
import plotly.graph_objects as go
import logging
from datetime import datetime
from typing import Dict, Optional, Tuple, Any, List, TypeVar

# Import from new structure
from app.db.repositories import TradeRepository
from app.core.statistical_refinements import winsorize_returns, calculate_ledoit_wolf_covariance

# Configure logging
logger = logging.getLogger(__name__)

# Type variable for generic function return type
T = TypeVar('T')

def normalize_percentage(value: float, default_decimal: bool = True) -> float:
    """
    Normalize a percentage value to either decimal (0.0-1.0) or percentage (0-100) format.

    Args:
        value: The value to normalize
        default_decimal: If True, normalize to decimal format (0.0-1.0),
                         otherwise normalize to percentage format (0-100)

    Returns:
        Normalized value in the requested format
    """
    if value is None:
        return 0.0

    # Determine if the value is likely in percentage format (>1.0) or decimal format (<=1.0)
    is_percentage_format = value > 1.0

    # Log the conversion for debugging
    logger.debug(f"Normalizing value: {value} (is_percentage_format={is_percentage_format}, default_decimal={default_decimal})")

    if default_decimal:
        # Convert to decimal format if needed
        result = value / 100.0 if is_percentage_format else value
    else:
        # Convert to percentage format if needed
        result = value * 100.0 if not is_percentage_format else value

    logger.debug(f"Normalized result: {result}")
    return result

class OptimisationEngine:
    """
    Advanced portfolio optimization engine using Modern Portfolio Theory.

    This implementation allows for:
    - Calculating the efficient frontier of portfolios
    - Finding the portfolio with maximum Sharpe ratio
    - Finding the portfolio with minimum variance
    - Handling various portfolio constraints (min/max weights, etc.)
    - Visualizing portfolio allocations and the efficient frontier

    The class is designed to be easily integrated with API frameworks.
    """

    def __init__(self,
                 companies_dict: Optional[Dict[str, str]] = None,
                 from_date: Optional[str] = None,
                 to_date: Optional[str] = None,
                 risk_free_rate: float = 0,
                 weight_bounds: Tuple[float, float] = (0, 1)):
        """
        Initialize the OptimisationEngine class

        Args:
            companies_dict: Dictionary mapping company names to their ticker symbols
            from_date: Start date for historical data analysis in YYYY-MM-DD format
            to_date: End date for historical data analysis in YYYY-MM-DD format
            risk_free_rate: Risk-free rate used in Sharpe ratio calculations
            weight_bounds: Default tuple of (min_weight, max_weight) for portfolio allocation constraints
        """
        self.companies_dict: Optional[Dict[str, str]] = companies_dict
        self.start_date: Optional[str] = from_date  # Keep internal variable name for backward compatibility
        self.end_date: Optional[str] = to_date      # Keep internal variable name for backward compatibility
        self.risk_free_rate: float = risk_free_rate
        self.weight_bounds: Tuple[float, float] = weight_bounds


        # Data storage
        self.mean_returns: Optional[pd.Series] = None
        self.cov_matrix: Optional[pd.DataFrame] = None
        self.assets: Optional[List[str]] = None
        self.raw_returns: Optional[pd.DataFrame] = None
        self.winsorized_returns: Optional[pd.DataFrame] = None

        # Note: We don't automatically call get_data() here anymore since it's async
        # Data will be loaded when needed by the appropriate methods

    async def get_data(self,
                 companies_dict: Optional[Dict[str, str]] = None,
                 from_date: Optional[str] = None,
                 to_date: Optional[str] = None) -> Tuple[pd.Series, pd.DataFrame]:
        """
        Download and process stock data from MongoDB.

        Args:
            companies_dict: Dictionary mapping company names to their ticker symbols
            from_date: Start date for historical data analysis in YYYY-MM-DD format
            to_date: End date for historical data analysis in YYYY-MM-DD format

        Returns:
            Tuple containing mean returns and covariance matrix

        Raises:
            ValueError: If required parameters are missing or data download fails
        """
        if companies_dict:
            self.companies_dict = companies_dict
        if from_date:
            self.start_date = from_date  # Keep internal variable name for backward compatibility
        if to_date:
            self.end_date = to_date  # Keep internal variable name for backward compatibility

        if self.companies_dict is None:
            raise ValueError("companies_dict is not set")
        if self.start_date is None:
            raise ValueError("from_date is not set")
        if self.end_date is None:
            raise ValueError("to_date is not set")

        # Store asset names for later use
        self.assets = list(self.companies_dict.values())

        # Get data from MongoDB
        returns = await self._get_data_from_mongodb()

        # Store the raw returns
        self.raw_returns = returns

        # Process the returns data (winsorization and covariance estimation)
        self._process_returns_data(returns)

        # Ensure we're not returning None values
        if self.mean_returns is None or self.cov_matrix is None:
            raise ValueError("Failed to calculate mean returns or covariance matrix")

        return self.mean_returns, self.cov_matrix

    def _process_returns_data(self, returns_data: pd.DataFrame) -> None:
        """
        Process returns data by applying winsorization and calculating covariance.

        Args:
            returns_data: DataFrame containing returns data
        """
        # Apply 2.5% winsorization to limit the influence of outliers
        try:
            self.winsorized_returns = winsorize_returns(returns_data=returns_data)
            returns_for_calc = self.winsorized_returns
        except Exception as e:
            logger.warning(f"Winsorization failed: {str(e)}. Using raw returns instead.")
            returns_for_calc = self.raw_returns

        # Calculate mean returns
        self.mean_returns = returns_for_calc.mean()

        # Calculate covariance matrix using Ledoit-Wolf shrinkage
        try:
            self.cov_matrix = calculate_ledoit_wolf_covariance(returns_for_calc)
        except Exception as e:
            logger.warning(f"Ledoit-Wolf covariance estimation failed: {str(e)}. Using sample covariance instead.")
            self.cov_matrix = returns_for_calc.cov()

    async def _get_data_from_mongodb(self) -> pd.DataFrame:
        """
        Fetch historical trade data from MongoDB and calculate returns.

        Returns:
            DataFrame of daily returns for each asset

        Raises:
            ValueError: If data retrieval fails or insufficient data is available
        """
        try:
            # Convert date strings to datetime objects
            start_date = datetime.strptime(self.start_date, "%Y-%m-%d")
            end_date = datetime.strptime(self.end_date, "%Y-%m-%d")

            # Create a dictionary to store price data for each asset
            price_data = {}

            # Fetch data for each asset using the repository
            for company_name, ticker_symbol in self.companies_dict.items():
                # Get historical trades for this ticker
                trades = await TradeRepository.get_historical_trades(ticker_symbol, start_date, end_date)

                if not trades:
                    raise ValueError(f"No trade data found for {company_name} ({ticker_symbol}) in the specified date range")

                logger.info(f"Retrieved {len(trades)} data points for {company_name} ({ticker_symbol})")

                # Extract dates and closing prices
                dates = [trade["tradeDate"] for trade in trades]
                prices = [float(trade["close"]) for trade in trades]

                # Check for duplicate dates
                date_price_dict = {}
                duplicate_count = 0
                for i, date in enumerate(dates):
                    # If duplicate date, use the latest price (assuming trades are sorted by date)
                    if date in date_price_dict:
                        duplicate_count += 1
                    date_price_dict[date] = prices[i]

                # Log if duplicates were found
                if duplicate_count > 0:
                    logger.warning(f"Found {duplicate_count} duplicate dates for {ticker_symbol}. Using latest prices.")

                # Create Series with unique dates
                unique_dates = list(date_price_dict.keys())
                unique_prices = [date_price_dict[date] for date in unique_dates]

                # Store in dictionary
                price_data[ticker_symbol] = pd.Series(unique_prices, index=unique_dates)

            # Convert to DataFrame
            price_df = pd.DataFrame(price_data)

            # Check if we have enough data
            if price_df.empty:
                raise ValueError("No data retrieved from MongoDB for the specified symbols and date range")

            if len(price_df) < 2:
                raise ValueError("Insufficient data points for portfolio optimization (minimum 2 required)")

            # Calculate daily returns
            # Use fill_method=None to avoid FutureWarning
            returns_df = price_df.pct_change(fill_method=None).dropna()

            # Check if we have enough data points after calculating returns
            if len(returns_df) == 0:
                raise ValueError(
                    f"No common trading days found for the selected companies between {self.start_date} and {self.end_date}. "
                    f"Please try a different date range or select different companies."
                )

            # Require at least 5 common trading days for reliable optimization
            if len(returns_df) < 5:
                error_message = (
                    f"Only {len(returns_df)} common trading days found for the selected companies. "
                    f"At least 5 common trading days are required for reliable optimization. "
                    f"Please try a different date range or select different companies with more overlapping trading history."
                )
                logger.error(error_message)
                raise ValueError(error_message)

            logger.info(f"Successfully calculated returns for {len(self.companies_dict)} assets over {len(returns_df)} days")

            return returns_df

        except Exception as e:
            logger.error(f"Error fetching data from MongoDB: {str(e)}")
            raise ValueError(f"Failed to retrieve data from MongoDB: {str(e)}")

    def portfolio_performance(self, weights: np.ndarray) -> Tuple[float, float]:
        """
        Calculate the expected return and standard deviation of a portfolio.

        Args:
            weights: Array of portfolio weights

        Returns:
            Tuple of (annualized_return, annualized_standard_deviation)

        Raises:
            ValueError: If mean returns or covariance matrix is not set
        """
        if self.mean_returns is None or self.cov_matrix is None:
            raise ValueError("Mean returns or covariance matrix is not set. Call get_data() first.")

        # Calculate the raw returns (not annualized)
        raw_returns = np.sum(self.mean_returns * weights)

        # Calculate the raw standard deviation (not annualized)
        raw_std = np.sqrt(np.dot(weights.T, np.dot(self.cov_matrix, weights)))

        # Annualize the returns and standard deviation
        # 252 is the number of trading days in a year
        annualized_returns = raw_returns * 252
        annualized_std = raw_std * np.sqrt(252)

        return annualized_returns, annualized_std

    def neg_sharpe_ratio(self, weights: np.ndarray) -> float:
        """
        Calculate the negative Sharpe ratio for optimization.

        Args:
            weights: Array of portfolio weights

        Returns:
            Negative Sharpe ratio (for minimization)
        """
        P_returns, P_std = self.portfolio_performance(weights)
        return -(P_returns - self.risk_free_rate) / P_std

    def _get_optimization_constraints(self,
                                     target_return: Optional[float] = None,
                                     custom_constraints: Optional[List[Dict]] = None) -> List[Dict]:
        """
        Generate optimization constraints based on configuration.

        Args:
            target_return: Optional target return for efficient frontier calculation
            custom_constraints: Optional list of custom constraints to add

        Returns:
            List of constraint dictionaries for scipy.optimize.minimize
        """
        constraints = []

        # The sum of the weights should be equal to 1
        constraints.append({'type': 'eq', 'fun': lambda x: np.sum(x) - 1})

        # Add target return constraint if specified
        if target_return is not None:
            constraints.append({'type': 'eq', 'fun': lambda x: self.portfolio_return(x) - target_return})

        # Add custom constraints if specified
        if custom_constraints:
            constraints.extend(custom_constraints)

        return constraints

    def _get_bounds(self, custom_bounds: Optional[List[Tuple[float, float]]] = None) -> List[Tuple[float, float]]:
        """
        Generate bounds for optimization based on configuration.

        Args:
            custom_bounds: Optional list of custom bounds for specific assets

        Returns:
            List of (min_weight, max_weight) tuples for each asset
        """
        if custom_bounds and len(custom_bounds) == len(self.assets):
            return custom_bounds

        # Use default bounds for all assets
        return [self.weight_bounds] * len(self.assets)

    def max_sharpe_ratio(self,
                         custom_bounds: Optional[List[Tuple[float, float]]] = None,
                         custom_constraints: Optional[List[Dict]] = None) -> Dict[str, Any]:
        """
        Find the portfolio weights that maximize the Sharpe ratio.

        Args:
            custom_bounds: Optional list of custom bounds for specific assets
            custom_constraints: Optional list of custom constraints to add

        Returns:
            Optimization result dictionary

        Raises:
            ValueError: If mean returns is not set
        """
        if self.mean_returns is None:
            raise ValueError("Mean returns is not set. Call get_data() first.")

        no_assets = len(self.mean_returns) # To ensure that the optimisation is performed only on assets for which we have valid data
        args = ()

        constraints = self._get_optimization_constraints(custom_constraints=custom_constraints)
        bounds = self._get_bounds(custom_bounds)

        # Initial guess: equal weight portfolio
        initial_guess = np.array([1.0 / no_assets] * no_assets)

        result = sp.minimize(self.neg_sharpe_ratio, initial_guess, args=args,
                            method='SLSQP', bounds=bounds, constraints=constraints)
        return result

    def portfolio_variance(self, weights: np.ndarray) -> float:
        """
        Calculate the variance (volatility squared) of the portfolio.

        Args:
            weights: Array of portfolio weights

        Returns:
            Portfolio variance
        """
        return self.portfolio_performance(weights)[1]

    def min_variance(self,
                    custom_bounds: Optional[List[Tuple[float, float]]] = None,
                    custom_constraints: Optional[List[Dict]] = None) -> Dict[str, Any]:
        """
        Find the portfolio weights that minimize portfolio variance.

        Args:
            custom_bounds: Optional list of custom bounds for specific assets
            custom_constraints: Optional list of custom constraints to add

        Returns:
            Optimization result dictionary

        Raises:
            ValueError: If mean returns is not set
        """
        if self.mean_returns is None:
            raise ValueError("Mean returns is not set. Call get_data() first.")

        no_assets = len(self.mean_returns) # To ensure that the optimisation is performed only on assets for which we have valid data
        args = ()

        constraints = self._get_optimization_constraints(custom_constraints=custom_constraints)
        bounds = self._get_bounds(custom_bounds)

        # Initial guess: equal weight portfolio
        initial_guess = np.array([1.0 / no_assets] * no_assets)

        result = sp.minimize(self.portfolio_variance, initial_guess, args=args,
                            method='SLSQP', bounds=bounds, constraints=constraints)
        return result

    def portfolio_return(self, weights: np.ndarray) -> float:
        """
        Calculate the expected return of the portfolio.

        Args:
            weights: Array of portfolio weights

        Returns:
            Expected portfolio return
        """
        return self.portfolio_performance(weights)[0]

    def efficient_frontier(self,
                          target_return: float,
                          custom_bounds: Optional[List[Tuple[float, float]]] = None,
                          custom_constraints: Optional[List[Dict]] = None) -> Dict[str, Any]:
        """
        Find the minimum variance portfolio for a given target return.

        Args:
            target_return: Target expected return for the portfolio (decimal format, e.g., 0.10 for 10%)
            custom_bounds: Optional list of custom bounds for specific assets
            custom_constraints: Optional list of custom constraints to add

        Returns:
            Optimization result dictionary

        Raises:
            ValueError: If mean returns is not set
        """
        if self.mean_returns is None:
            raise ValueError("Mean returns is not set. Call get_data() first.")

        # Ensure target_return is in decimal form (e.g., 0.10 for 10%)
        target_return = normalize_percentage(target_return, default_decimal=True)

        no_assets = len(self.mean_returns) # To ensure that the optimisation is performed only on assets for which we have valid data
        args = ()

        constraints = self._get_optimization_constraints(
            target_return=target_return,
            custom_constraints=custom_constraints
        )
        bounds = self._get_bounds(custom_bounds)

        # Initial guess: equal weight portfolio
        initial_guess = np.array([1.0 / no_assets] * no_assets)

        result = sp.minimize(self.portfolio_variance, initial_guess, args=args,
                            method='SLSQP', bounds=bounds, constraints=constraints)
        return result

    def calculated_results(self,
                          custom_bounds: Optional[List[Tuple[float, float]]] = None,
                          custom_constraints: Optional[List[Dict]] = None) -> Tuple:
        """
        Calculate key portfolio optimization results.

        Args:
            custom_bounds: Optional list of custom bounds for specific assets
            custom_constraints: Optional list of custom constraints to add

        Returns:
            Tuple containing:
            - max_sharpe_ratio_returns: Annualized returns of the max Sharpe ratio portfolio (%)
            - max_sharpe_ratio_std: Annualized standard deviation of max Sharpe ratio portfolio (%)
            - max_sharpe_ratio_allocation: DataFrame with allocation percentages for max Sharpe ratio portfolio
            - min_var_returns: Annualized returns of the minimum variance portfolio (%)
            - min_var_std: Annualized standard deviation of minimum variance portfolio (%)
            - min_var_allocation: DataFrame with allocation percentages for minimum variance portfolio
            - efficient_list: List of standard deviations for points on the efficient frontier
            - target_returns: List of returns for points on the efficient frontier

        Raises:
            ValueError: If mean returns is not set
        """
        if self.mean_returns is None:
            raise ValueError("Mean returns is not set. Call get_data() first.")

        # Maximum Sharpe Ratio Portfolio
        max_sharpe_ratio_portfolio = self.max_sharpe_ratio(
            custom_bounds=custom_bounds,
            custom_constraints=custom_constraints
        )
        max_sharpe_ratio_returns, max_sharpe_ratio_std = self.portfolio_performance(max_sharpe_ratio_portfolio['x'])
        max_sharpe_ratio_allocation = pd.DataFrame(max_sharpe_ratio_portfolio['x'], index=self.mean_returns.index, columns=['allocation'])
        # Keep allocations as decimals (0.0 to 1.0)
        max_sharpe_ratio_allocation.allocation = [round(i, 4) for i in max_sharpe_ratio_allocation.allocation]

        # Minimum Variance Portfolio
        min_var_portfolio = self.min_variance(
            custom_bounds=custom_bounds,
            custom_constraints=custom_constraints
        )
        min_var_returns, min_var_std = self.portfolio_performance(min_var_portfolio['x'])
        min_var_allocation = pd.DataFrame(min_var_portfolio['x'], index=self.mean_returns.index, columns=['allocation'])
        # Keep allocations as decimals (0.0 to 1.0)
        min_var_allocation.allocation = [round(i, 4) for i in min_var_allocation.allocation]

        # Efficient Frontier
        efficient_list = []
        target_returns = np.linspace(min_var_returns, max_sharpe_ratio_returns, 20)
        for target in target_returns:
            efficient_list.append(self.efficient_frontier(
                target,
                custom_bounds=custom_bounds,
                custom_constraints=custom_constraints
            )['fun'])

        # Keep returns and standard deviation as decimals for internal calculations
        # Round to 4 decimal places for precision
        max_sharpe_ratio_returns = round(max_sharpe_ratio_returns, 4)
        max_sharpe_ratio_std = round(max_sharpe_ratio_std, 4)

        # Keep returns and standard deviation as decimals for internal calculations
        # Round to 4 decimal places for precision
        min_var_returns = round(min_var_returns, 4)
        min_var_std = round(min_var_std, 4)

        return (max_sharpe_ratio_returns, max_sharpe_ratio_std, max_sharpe_ratio_allocation,
                min_var_returns, min_var_std, min_var_allocation, efficient_list, target_returns)

    def get_portfolio_summary(self,
                             custom_bounds: Optional[List[Tuple[float, float]]] = None,
                             custom_constraints: Optional[List[Dict]] = None) -> Dict[str, Any]:
        """
        Get a structured summary of portfolio optimization results suitable for API responses.

        Args:
            custom_bounds: Optional list of custom bounds for specific assets
            custom_constraints: Optional list of custom constraints to add

        Returns:
            Dictionary containing structured portfolio optimization results

        Raises:
            ValueError: If mean returns is not set
        """
        results = self.calculated_results(
            custom_bounds=custom_bounds,
            custom_constraints=custom_constraints
        )

        max_sharpe_returns, max_sharpe_std, max_sharpe_allocation, min_var_returns, min_var_std, min_var_allocation, efficient_list, target_returns = results

        # Convert allocations to dictionaries and from decimals to percentages for API response
        max_sharpe_allocations_dict = {
            ticker: round(float(allocation * 100), 2) for ticker, allocation in
            zip(max_sharpe_allocation.index, max_sharpe_allocation['allocation'])
        }

        min_var_allocations_dict = {
            ticker: round(float(allocation * 100), 2) for ticker, allocation in
            zip(min_var_allocation.index, min_var_allocation['allocation'])
        }

        # Create efficient frontier points
        # Convert from decimals to percentages for API response
        efficient_frontier_points = [
            {"volatility": round(float(ef_std*100), 2), "return": round(float(target*100), 2)} for ef_std, target in
            zip(efficient_list, target_returns)
        ]

        # Create response dictionary
        return {
            "max_sharpe_ratio": {
                "performance": {
                    "returns": round(float(max_sharpe_returns * 100), 2),  # Convert to percentage and round to 2 decimal places
                    "volatility": round(float(max_sharpe_std * 100), 2),  # Convert to percentage and round to 2 decimal places
                    "sharpe_ratio": round(float((max_sharpe_returns - self.risk_free_rate) / max_sharpe_std), 2) if max_sharpe_std > 0 else 0
                },
                "allocations": max_sharpe_allocations_dict
            },
            "min_variance": {
                "performance": {
                    "returns": round(float(min_var_returns * 100), 2),  # Convert to percentage and round to 2 decimal places
                    "volatility": round(float(min_var_std * 100), 2),  # Convert to percentage and round to 2 decimal places
                    "sharpe_ratio": round(float((min_var_returns - self.risk_free_rate) / min_var_std), 2) if min_var_std > 0 else 0
                },
                "allocations": min_var_allocations_dict
            },
            "efficient_frontier": efficient_frontier_points,
            "constraints": {
                "weight_bounds": self.weight_bounds
            }
            # Note: 2.5% winsorization is always applied internally but not exposed to users
        }

    def get_efficient_frontier(self,
                              custom_bounds: Optional[List[Tuple[float, float]]] = None,
                              custom_constraints: Optional[List[Dict]] = None,
                              num_points: int = 10,
                              min_target_return: Optional[float] = None) -> List[Dict[str, Any]]:
        """
        Get the efficient frontier data points.

        Args:
            custom_bounds: Optional list of custom bounds for specific assets
            custom_constraints: Optional list of custom constraints to add
            num_points: Number of points to generate along the efficient frontier (default: 10)
            min_target_return: Minimum target return to include as a decimal (e.g., 0.10 for 10%)

        Returns:
            List of dictionaries containing volatility, return, and weights for each point on the efficient frontier
        """
        # Calculate the efficient frontier and optimal portfolios
        results = self.calculated_results(
            custom_bounds=custom_bounds,
            custom_constraints=custom_constraints
        )

        max_sharpe_returns = results[0]
        max_sharpe_std = results[1]
        max_sharpe_allocation = results[2]
        min_var_returns = results[3]
        min_var_std = results[4]
        min_var_allocation = results[5]
        # We don't need these for this method
        # efficient_list = results[6]
        # target_returns = results[7]

        # Create a list of efficient frontier points with weights
        frontier_points = []

        # Add the minimum variance portfolio
        min_var_weights = {
            ticker: float(allocation) for ticker, allocation in
            zip(min_var_allocation.index, min_var_allocation['allocation'])
        }

        frontier_points.append({
            "volatility": float(min_var_std),
            "return": float(min_var_returns),
            "weights": min_var_weights
        })

        # Generate additional points along the efficient frontier
        # Use more points between min_var_returns and max_sharpe_returns
        additional_returns = np.linspace(min_var_returns, max_sharpe_returns, num_points)[1:-1]  # Exclude endpoints

        # If a minimum target return is specified and it's lower than the minimum variance return,
        # try to generate portfolios with lower returns
        if min_target_return is not None and min_target_return < min_var_returns:
            logger.info(f"Attempting to generate portfolios with returns below minimum variance ({min_var_returns}%)")

            # Try different combinations of assets to find lower return portfolios
            for i, ticker1 in enumerate(self.assets):
                for j, ticker2 in enumerate(self.assets[i+1:], i+1):
                    # Try a portfolio with just these two assets
                    try:
                        # Create weights with only these two assets
                        weights = np.zeros(len(self.assets))
                        weights[i] = 0.5  # 50% in first asset
                        weights[j] = 0.5  # 50% in second asset

                        # Calculate performance
                        portfolio_return, portfolio_std = self.portfolio_performance(weights)

                        # Keep as decimals for internal calculations
                        portfolio_return_dec = float(round(portfolio_return, 4))
                        portfolio_std_dec = float(round(portfolio_std, 4))

                        # If this portfolio has a return closer to the target, add it
                        if portfolio_return_dec < min_var_returns and portfolio_return_dec >= min_target_return:
                            # Create weights dictionary
                            weights_dict = {}
                            for k, ticker in enumerate(self.assets):
                                weight = float(round(weights[k], 4))  # Keep as decimal
                                if weight > 0.0001:  # Only include weights > 0.01%
                                    weights_dict[ticker] = weight

                            # Add to frontier points
                            frontier_points.append({
                                "volatility": portfolio_std_dec,
                                "return": portfolio_return_dec,
                                "weights": weights_dict
                            })

                            logger.info(f"Generated lower return portfolio: {portfolio_return_dec:.4f} return, {portfolio_std_dec:.4f} volatility")

                    except Exception as e:
                        logger.warning(f"Could not generate lower return portfolio with {ticker1} and {ticker2}: {str(e)}")
                        continue

        # Generate portfolios along the efficient frontier
        for target_return in additional_returns:
            try:
                # Optimize for this specific target return
                # Ensure target_return is in decimal form (e.g., 0.10 for 10%)
                target_return_decimal = normalize_percentage(target_return, default_decimal=True)

                result = self.efficient_frontier(
                    target_return_decimal,  # Use decimal form for optimization
                    custom_bounds=custom_bounds,
                    custom_constraints=custom_constraints
                )

                # Get the portfolio performance
                portfolio_return, portfolio_std = self.portfolio_performance(result['x'])

                # Keep as decimals for internal calculations
                portfolio_return = float(round(portfolio_return, 4))
                portfolio_std = float(round(portfolio_std, 4))

                # Create weights dictionary
                weights = {}
                for i, ticker in enumerate(self.mean_returns.index):
                    weight = float(round(result['x'][i], 4))  # Keep as decimal
                    if weight > 0.0001:  # Only include weights > 0.01%
                        weights[ticker] = weight

                # Add to frontier points
                frontier_points.append({
                    "volatility": portfolio_std,
                    "return": portfolio_return,
                    "weights": weights
                })

            except Exception as e:
                logger.warning(f"Could not optimize for target return {target_return}%: {str(e)}")
                continue

        # Add the maximum Sharpe ratio portfolio
        max_sharpe_weights = {
            ticker: float(allocation) for ticker, allocation in
            zip(max_sharpe_allocation.index, max_sharpe_allocation['allocation'])
        }

        frontier_points.append({
            "volatility": float(max_sharpe_std),
            "return": float(max_sharpe_returns),
            "weights": max_sharpe_weights
        })

        # Log the weights for debugging
        logger.info(f"Min var weights: {min_var_weights}")
        logger.info(f"Max sharpe weights: {max_sharpe_weights}")
        logger.info(f"Generated {len(frontier_points)} points on the efficient frontier")

        # Sort by return
        frontier_points.sort(key=lambda x: x["return"])

        return frontier_points

    def generate_efficient_frontier_figure(self,
                                          custom_bounds: Optional[List[Tuple[float, float]]] = None,
                                          custom_constraints: Optional[List[Dict]] = None) -> go.Figure:
        """
        Generate a Plotly figure of the efficient frontier.

        Args:
            custom_bounds: Optional list of custom bounds for specific assets
            custom_constraints: Optional list of custom constraints to add

        Returns:
            Plotly Figure object for the efficient frontier
        """
        # Get the calculated results (we only need some of these values for the plot)
        results = self.calculated_results(
            custom_bounds=custom_bounds,
            custom_constraints=custom_constraints
        )
        max_sharpe_ratio_returns = results[0]
        max_sharpe_ratio_std = results[1]
        min_var_returns = results[3]
        min_var_std = results[4]
        efficient_list = results[6]
        target_returns = results[7]

        # Max Sharpe Ratio Portfolio
        MaxSharpeRatio = go.Scatter(
            x=[max_sharpe_ratio_std],
            y=[max_sharpe_ratio_returns],
            mode='markers',
            name='Max Sharpe Ratio Portfolio',
            marker=dict(color='red', size=14, line=dict(color='black', width=3))
        )

        # Minimum Variance Portfolio
        MinVar = go.Scatter(
            x=[min_var_std],
            y=[min_var_returns],
            mode='markers',
            name='Minimum Variance Portfolio',
            marker=dict(color='green', size=14, line=dict(color='black', width=3))
        )

        # Efficient Frontier Plot
        EfficientFrontierCurve = go.Scatter(
            x=[round(ef_std*100, 2) for ef_std in efficient_list],
            y=[round(target*100, 2) for target in target_returns],
            mode='lines',
            name='Efficient Frontier',
            line=dict(color='black', width=4, dash='dashdot')
        )

        data = [MaxSharpeRatio, MinVar, EfficientFrontierCurve]

        layout = go.Layout(
            title='Portfolio Optimisation with Efficient Frontier',
            xaxis=dict(title='Annualised Volatility (%)'),
            yaxis=dict(title='Annualised Return (%)'),
            showlegend=True,
            legend=dict(
                x=0.75, y=0, traceorder='normal',
                bgcolor='#E2E2E2',
                bordercolor='black',
                borderwidth=2),
            width=800,
            height=600
        )

        return go.Figure(data=data, layout=layout)

    def efficient_frontier_plot(self,
                               custom_bounds: Optional[List[Tuple[float, float]]] = None,
                               custom_constraints: Optional[List[Dict]] = None) -> None:
        """
        Display the efficient frontier plot.

        Args:
            custom_bounds: Optional list of custom bounds for specific assets
            custom_constraints: Optional list of custom constraints to add
        """
        fig = self.generate_efficient_frontier_figure(
            custom_bounds=custom_bounds,
            custom_constraints=custom_constraints
        )
        return fig.show()

    def generate_allocation_figure(self,
                                  allocation_type: str = 'max_sharpe',
                                  custom_bounds: Optional[List[Tuple[float, float]]] = None,
                                  custom_constraints: Optional[List[Dict]] = None) -> go.Figure:
        """
        Generate a Plotly figure for portfolio allocation.

        Args:
            allocation_type: Type of allocation to visualize ('max_sharpe' or 'min_variance')
            custom_bounds: Optional list of custom bounds for specific assets
            custom_constraints: Optional list of custom constraints to add

        Returns:
            Plotly Figure object for the portfolio allocation pie chart
        """
        # Get the allocation data from calculated_results
        results = self.calculated_results(
            custom_bounds=custom_bounds,
            custom_constraints=custom_constraints
        )
        max_sharpe_returns = results[0]
        max_sharpe_allocation = results[2]
        min_var_returns = results[3]
        min_var_allocation = results[5]

        if allocation_type == 'max_sharpe':
            allocation = max_sharpe_allocation
            returns = max_sharpe_returns
            title = 'Maximum Sharpe Ratio Portfolio'
        else:  # min_variance
            allocation = min_var_allocation
            returns = min_var_returns
            title = 'Minimum Variance Portfolio'

        # Filter out assets with very small allocation for cleaner visualization
        filtered_allocation = allocation[allocation['allocation'] > 0.5]  # Only show allocations > 0.5%

        # Create the pie chart
        fig = go.Figure(data=[go.Pie(
            labels=filtered_allocation.index,
            values=filtered_allocation['allocation'],
            hole=0.3,
            textinfo='label+percent',
            insidetextorientation='radial',
            marker=dict(
                line=dict(color='#000000', width=1)
            )
        )])

        fig.update_layout(
            title_text=f'{title} Allocation (Expected Return: {returns}%)',
            showlegend=True,
            legend=dict(
                orientation="h",
                yanchor="bottom",
                y=-0.1,
                xanchor="center",
                x=0.5
            ),
            width=800,
            height=600
        )

        return fig

    def plot_allocation(self,
                       allocation_type: str = 'max_sharpe',
                       custom_bounds: Optional[List[Tuple[float, float]]] = None,
                       custom_constraints: Optional[List[Dict]] = None) -> None:
        """
        Display a pie chart of portfolio allocation.

        Args:
            allocation_type: Type of allocation to visualize ('max_sharpe' or 'min_variance')
            custom_bounds: Optional list of custom bounds for specific assets
            custom_constraints: Optional list of custom constraints to add
        """
        fig = self.generate_allocation_figure(
            allocation_type=allocation_type,
            custom_bounds=custom_bounds,
            custom_constraints=custom_constraints
        )
        return fig.show()

"""
Portfolio interpolation utilities.

This module provides functions for interpolating between portfolios on the efficient frontier.
"""

import logging
from typing import Dict, List, Any, Optional

logger = logging.getLogger(__name__)

def interpolate_portfolios(
    target_return: float,
    portfolios: List[Dict[str, Any]],
    tolerance: float = 0.001
) -> Optional[Dict[str, Any]]:
    """
    Interpolate between portfolios to find one with the target return.

    Args:
        target_return: The target return as a decimal (e.g., 0.05 for 5%)
        portfolios: List of portfolios sorted by return (with returns as decimals)
        tolerance: Tolerance for exact match as a decimal (e.g., 0.001 for 0.1%)

    Returns:
        Interpolated portfolio or None if interpolation is not possible
    """
    # Sort portfolios by return
    sorted_portfolios = sorted(portfolios, key=lambda x: x["return"])

    # Check if we already have a portfolio with the target return within tolerance
    for portfolio in sorted_portfolios:
        if abs(portfolio["return"] - target_return) <= tolerance:
            logger.info(f"Found portfolio with return {portfolio['return']:.4f} within tolerance of target {target_return:.4f}")
            return portfolio

    # Find the two portfolios that bracket the target return
    lower_portfolio = None
    upper_portfolio = None

    for i in range(len(sorted_portfolios) - 1):
        if sorted_portfolios[i]["return"] <= target_return <= sorted_portfolios[i+1]["return"]:
            lower_portfolio = sorted_portfolios[i]
            upper_portfolio = sorted_portfolios[i+1]
            break

    if lower_portfolio is None or upper_portfolio is None:
        logger.warning(f"Cannot interpolate: target return {target_return:.4f} is outside the range of available portfolios")
        return None

    # Calculate interpolation factor
    lower_return = lower_portfolio["return"]
    upper_return = upper_portfolio["return"]

    # Avoid division by zero
    if upper_return == lower_return:
        return lower_portfolio

    # Calculate the interpolation factor (0 to 1)
    factor = (target_return - lower_return) / (upper_return - lower_return)

    logger.info(f"Interpolating between portfolios with returns {lower_return:.4f} and {upper_return:.4f}")
    logger.info(f"Interpolation factor: {factor}")

    # Interpolate the weights
    all_tickers = set(lower_portfolio["weights"].keys()) | set(upper_portfolio["weights"].keys())
    interpolated_weights = {}

    for ticker in all_tickers:
        lower_weight = lower_portfolio["weights"].get(ticker, 0)
        upper_weight = upper_portfolio["weights"].get(ticker, 0)
        interpolated_weight = lower_weight * (1 - factor) + upper_weight * factor

        # Only include weights above a threshold
        if interpolated_weight > 0.01:
            interpolated_weights[ticker] = round(interpolated_weight, 2)

    # Normalize weights to ensure they sum to 1.0
    total_weight = sum(interpolated_weights.values())
    if total_weight > 0:
        interpolated_weights = {
            ticker: round(weight / total_weight, 4)
            for ticker, weight in interpolated_weights.items()
        }

    # Interpolate volatility
    interpolated_volatility = lower_portfolio["volatility"] * (1 - factor) + upper_portfolio["volatility"] * factor

    # Create the interpolated portfolio
    interpolated_portfolio = {
        "return": round(target_return, 4),
        "volatility": round(interpolated_volatility, 4),
        "weights": interpolated_weights,
        "interpolated": True
    }

    # Calculate Sharpe ratio (using risk-free rate from config if available)
    from app.config import DEFAULT_RISK_FREE_RATE
    if interpolated_volatility > 0:
        interpolated_portfolio["sharpe_ratio"] = round((target_return - DEFAULT_RISK_FREE_RATE) / interpolated_volatility, 4)

    return interpolated_portfolio

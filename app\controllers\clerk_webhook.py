"""
Clerk Webhook Controller

This module provides endpoints for handling Clerk webhook events.
"""

from typing import Dict, Any, Optional
import json
from datetime import datetime, timezone
import logging
import traceback
from fastapi import Request, HTTPException, Header, APIRouter
from svix.webhooks import Webhook, WebhookVerificationError
from app.services.user_service import UserService
from app.config import settings

# Configure logging
logger = logging.getLogger(__name__)

# Create router for webhook endpoints
webhook_router = APIRouter(prefix="/webhooks", tags=["webhooks"])

# Webhook route
@webhook_router.post("/clerk")
async def clerk_webhook_handler(
    request: Request,
    svix_id: str = Header(None, alias="svix-id"),
    svix_timestamp: str = Header(None, alias="svix-timestamp"),
    svix_signature: str = Header(None, alias="svix-signature")
):
    """Handle webhook events from <PERSON>"""
    logger.info("Received Clerk webhook request")
    logger.debug("Svix headers - ID: %s, Timestamp: %s, Signature: %s",
                svix_id, svix_timestamp,
                svix_signature)
    return await WebhookController.process_webhook(
        request, svix_id, svix_timestamp, svix_signature
    )

class WebhookController:
    """
    Controller for handling Clerk webhook events
    """

    @staticmethod
    async def verify_webhook_signature(
        request: Request,
        svix_id: Optional[str] = None,
        svix_timestamp: Optional[str] = None,
        svix_signature: Optional[str] = None
    ) -> Dict:
        """
        Verify that the webhook request is coming from Clerk using Svix signature
        """
        logger.info("Starting webhook signature verification")

        if not svix_id or not svix_timestamp or not svix_signature:
            logger.error("Missing Svix headers")
            logger.debug("Headers received - ID: %s, Timestamp: %s, Signature: %s", svix_id, svix_timestamp, svix_signature)
            raise HTTPException(status_code=401, detail="Missing Svix headers")

        # Get request body as bytes
        payload = await request.body()
        logger.debug("Raw payload: %s", payload.decode('utf-8'))

        # Get the signing secret from your environment
        signing_secret = settings.CLERK_WEBHOOK_SIGNING_SECRET

        # Create headers dictionary for Svix verification
        headers = {
            "svix-id": svix_id,
            "svix-timestamp": svix_timestamp,
            "svix-signature": svix_signature
        }

        try:
            # Use Svix library for verification
            wh = Webhook(signing_secret)
            # Verify the payload
            wh.verify(payload, headers)
            logger.info("Signature verification successful")
            return json.loads(payload)
        except WebhookVerificationError as e:
            logger.error("Signature verification failed: %s", str(e))
            raise HTTPException(status_code=401, detail="Invalid signature") from e
        except Exception as e:
            logger.error("Webhook verification error: %s", str(e))
            raise HTTPException(status_code=500, detail=f"Error verifying webhook: {str(e)}") from e

    @staticmethod
    async def extract_user_data(user_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Extract relevant user data from Clerk webhook payload
        """
        # Extract user ID
        clerk_user_id = user_data.get("id")

        # Extract primary email
        primary_email_id = user_data.get("primary_email_address_id")
        email_addresses = user_data.get("email_addresses", [])

        email = None
        if primary_email_id and email_addresses:
            for email_obj in email_addresses:
                if email_obj.get("id") == primary_email_id:
                    email = email_obj.get("email_address")
                    break

        # Extract user metadata
        first_name = user_data.get("first_name")
        last_name = user_data.get("last_name")
        profile_image_url = user_data.get("profile_image_url")

        # Extract any custom user metadata (if available)
        public_metadata = user_data.get("public_metadata", {})
        private_metadata = user_data.get("private_metadata", {})

        # Create a cleaned user data object
        return {
            "clerk_user_id": clerk_user_id,
            "email": email,
            "first_name": first_name,
            "last_name": last_name,
            "profile_image_url": profile_image_url,
            "public_metadata": public_metadata,
            "private_metadata": private_metadata
        }

    @staticmethod
    async def handle_user_created(user_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Handle user.created event from Clerk
        """
        # Extract user information
        extracted_data = await WebhookController.extract_user_data(user_data)
        clerk_user_id = extracted_data["clerk_user_id"]

        # Create user in MongoDB if not exists
        await UserService.get_or_create_user(clerk_user_id, extracted_data)

        return {
            "status": "success",
            "action": "user_created",
            "user_id": clerk_user_id
        }

    @staticmethod
    async def handle_user_updated(user_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Handle user.updated event from Clerk
        """
        # Extract user information
        extracted_data = await WebhookController.extract_user_data(user_data)
        clerk_user_id = extracted_data["clerk_user_id"]

        # Update user in MongoDB
        await UserService.update_user_basic_info(clerk_user_id, extracted_data)

        return {
            "status": "success",
            "action": "user_updated",
            "user_id": clerk_user_id
        }

    @staticmethod
    async def handle_user_deleted(user_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Handle user.deleted event from Clerk
        """
        clerk_user_id = user_data.get("id")

        if not clerk_user_id:
            raise HTTPException(status_code=400, detail="Missing user ID in deletion event")

        # Delete user from MongoDB
        await UserService.delete_user(clerk_user_id)

        return {
            "status": "success",
            "action": "user_deleted",
            "user_id": clerk_user_id
        }

    @staticmethod
    async def handle_email_created(data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Handle email.created event from Clerk
        """
        # Extract the user ID and email information
        email_data = data.get("email_address", {})
        clerk_user_id = email_data.get("user_id")
        email = email_data.get("email_address")

        if not clerk_user_id or not email:
            raise HTTPException(status_code=400, detail="Missing user ID or email in event")

        # Get the user and update their email
        await UserService.update_user_basic_info(clerk_user_id, {"email": email})

        return {
            "status": "success",
            "action": "email_created",
            "user_id": clerk_user_id
        }

    @staticmethod
    async def handle_session_created(data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Handle session.created event from Clerk (user login)
        """
        # Extract user ID from session data
        clerk_user_id = data.get("user_id")
        if not clerk_user_id:
            raise HTTPException(status_code=400, detail="Missing user ID in session event")

        # Update user's last login time
        # Note: update_last_login method needs to be implemented in UserService
        # For now, we'll update basic info with a login timestamp
        await UserService.update_user_basic_info(clerk_user_id, {
            "last_login": datetime.now(timezone.utc).isoformat()
        })

        return {
            "status": "success",
            "action": "session_created",
            "user_id": clerk_user_id
        }

    @staticmethod
    async def process_webhook(
        request: Request,
        svix_id: str = Header(None, alias="svix-id"),
        svix_timestamp: str = Header(None, alias="svix-timestamp"),
        svix_signature: str = Header(None, alias="svix-signature")
    ) -> Dict[str, Any]:
        """
        Process incoming Clerk webhook
        """
        try:
            # Verify webhook signature and get payload
            payload = await WebhookController.verify_webhook_signature(
                request, svix_id, svix_timestamp, svix_signature
            )

            # Extract event type and data
            event_type = payload.get("type")
            event_data = payload.get("data", {})

            # Process different event types
            if event_type == "user.created":
                return await WebhookController.handle_user_created(event_data)

            elif event_type == "user.updated":
                return await WebhookController.handle_user_updated(event_data)

            elif event_type == "user.deleted":
                return await WebhookController.handle_user_deleted(event_data)

            elif event_type == "email.created":
                return await WebhookController.handle_email_created(event_data)

            elif event_type == "session.created":
                return await WebhookController.handle_session_created(event_data)

            # Add more event handlers as needed

            # Default handler for unprocessed events
            return {
                "status": "success",
                "event": event_type,
                "message": "Event received but no specific handler implemented"
            }

        except HTTPException:
            # Re-raise HTTP exceptions
            raise
        except Exception as e:
            # Log error and return 500 for any other exceptions
            traceback.print_exc()
            raise HTTPException(
                status_code=500,
                detail=f"Error processing webhook: {str(e)}"
            ) from e

"""
Database Session

This module handles the database connection and session management.
It ensures proper cleanup of MongoDB clients when event loops are closed
to prevent connection leaks.
"""

import logging
import asyncio
import weakref
import gc
import time
from typing import Optional, Dict, Set, List
from motor.motor_asyncio import AsyncIOMotorClient, AsyncIOMotorDatabase
from app.config import settings

# Configure logging
logger = logging.getLogger(__name__)

# Define database at module level
database: Optional[AsyncIOMotorDatabase] = None

# Store clients for different event loops using weak references
# This allows event loops to be garbage collected
_clients: Dict[int, AsyncIOMotorClient] = {}  # Keyed by event loop ID
_databases: Dict[int, AsyncIOMotorDatabase] = {}  # Keyed by event loop ID
_loop_refs: Dict[int, weakref.ref] = {}  # Weak references to event loops
_client_ref_counts: Dict[int, int] = {}  # Reference count for each client
_client_last_used: Dict[int, float] = {}  # Last used timestamp for each client

# Track active event loop IDs
_active_loop_ids: Set[int] = set()

# Dictionary of locks for each event loop
_locks: Dict[int, asyncio.Lock] = {}

async def close_client(loop_id: int, force: bool = False) -> None:
    """
    Close the MongoDB client associated with the given event loop ID.

    Args:
        loop_id: The ID of the event loop whose client should be closed
        force: If True, close the client regardless of reference count
    """
    # Use a simple flag to avoid lock issues
    if loop_id in _clients:
        try:
            # Check reference count
            ref_count = _client_ref_counts.get(loop_id, 0)

            if ref_count > 0 and not force:
                logger.debug(f"Not closing MongoDB client for event loop {loop_id} - still in use (ref count: {ref_count})")
                return

            logger.debug(f"Closing MongoDB client for event loop {loop_id}")
            _clients[loop_id].close()

            # Remove from all dictionaries
            if loop_id in _clients:
                del _clients[loop_id]
            if loop_id in _databases:
                del _databases[loop_id]
            if loop_id in _loop_refs:
                del _loop_refs[loop_id]
            if loop_id in _client_ref_counts:
                del _client_ref_counts[loop_id]
            if loop_id in _client_last_used:
                del _client_last_used[loop_id]
            if loop_id in _locks:
                del _locks[loop_id]

            _active_loop_ids.discard(loop_id)
        except Exception as e:
            logger.error(f"Error closing MongoDB client for event loop {loop_id}: {str(e)}")

async def close_all_clients(force: bool = True) -> None:
    """
    Close all MongoDB clients for all event loops.
    This should be called during application shutdown.

    Args:
        force: If True, close all clients regardless of reference count
    """
    logger.info(f"Closing all MongoDB clients ({len(_clients)} clients)")
    # Create a copy of the keys to avoid modification during iteration
    loop_ids = list(_clients.keys())
    for loop_id in loop_ids:
        await close_client(loop_id, force=force)

async def cleanup_closed_loops() -> None:
    """
    Clean up MongoDB clients for event loops that have been closed or garbage collected.
    This helps prevent connection leaks.

    This function is more conservative now and will only clean up connections
    that are definitely not in use (ref count = 0).
    """
    try:
        # Get a list of loop IDs that might need cleanup
        # We'll be more conservative and only check loops with ref count 0
        loop_ids_to_check = []
        for loop_id in list(_active_loop_ids):
            if _client_ref_counts.get(loop_id, 1) == 0:  # Only check if ref count is 0
                loop_ids_to_check.append(loop_id)

        if not loop_ids_to_check:
            # No loops to clean up
            return

        logger.debug(f"Checking {len(loop_ids_to_check)} loops with ref count 0 for cleanup")

        for loop_id in loop_ids_to_check:
            # Check if the loop reference still exists and is not closed
            loop_ref = _loop_refs.get(loop_id)
            if loop_ref is None:
                # Reference is gone, clean up
                logger.info(f"Cleaning up MongoDB client for garbage collected event loop {loop_id}")
                await close_client(loop_id, force=False)  # Only close if not in use
                continue

            # Get the actual loop object if it still exists
            loop = loop_ref()
            if loop is None:
                # Loop has been garbage collected
                logger.info(f"Cleaning up MongoDB client for garbage collected event loop {loop_id}")
                await close_client(loop_id, force=False)  # Only close if not in use
            elif loop.is_closed():
                # Loop exists but is closed
                logger.info(f"Cleaning up MongoDB client for closed event loop {loop_id}")
                await close_client(loop_id, force=False)  # Only close if not in use

        # We'll skip checking for orphaned clients as it's causing more problems than it solves
    except Exception as e:
        logger.error(f"Error in cleanup_closed_loops: {str(e)}")

async def initialize_database() -> AsyncIOMotorDatabase:
    """
    Initialize the MongoDB database connection for the current event loop.

    This function creates a new MongoDB client for the current event loop if one
    doesn't already exist. This ensures that each event loop has its own client,
    which prevents "Event loop is closed" errors when using the client from
    different event loops.

    Returns:
        AsyncIOMotorDatabase: The MongoDB database instance
    """
    global database

    try:
        # Get the current event loop
        try:
            current_loop = asyncio.get_running_loop()
        except RuntimeError:
            # No running event loop, create a new one
            logger.debug("No running event loop found, creating a new one for database connection")
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            current_loop = loop

        # Get the loop ID
        loop_id = id(current_loop)

        # Create a lock for this event loop if it doesn't exist
        if loop_id not in _locks:
            _locks[loop_id] = asyncio.Lock()

        # Get the lock for this event loop
        lock = _locks[loop_id]

        # Use the lock specific to this event loop
        async with lock:
            # Check if we already have a client for this event loop
            if loop_id in _clients:
                logger.debug(f"Using existing MongoDB client for event loop {loop_id}")
                # Increment reference count
                _client_ref_counts[loop_id] = _client_ref_counts.get(loop_id, 0) + 1
                # Update last used timestamp
                _client_last_used[loop_id] = time.time()
                return _databases[loop_id]

            # Initialize MongoDB Client for this event loop
            logger.debug(f"Creating new MongoDB client for event loop {loop_id}")
            client = AsyncIOMotorClient(settings.MONGO_URI)
            db = client[settings.DATABASE_NAME]

            # Store the client and database for this event loop
            _clients[loop_id] = client
            _databases[loop_id] = db

            # Store a weak reference to the event loop
            _loop_refs[loop_id] = weakref.ref(current_loop)

            # Initialize reference count
            _client_ref_counts[loop_id] = 1

            # Set last used timestamp
            _client_last_used[loop_id] = time.time()

            # Track this loop ID as active
            _active_loop_ids.add(loop_id)

            # Also set the global database variable for backward compatibility
            database = db

            logger.info(f"Connected to MongoDB database: {settings.DATABASE_NAME} (event loop: {loop_id}, ref count: 1)")
            return db
    except Exception as e:
        logger.error(f"Failed to connect to MongoDB: {str(e)}")
        raise

# Initialize the database connection when the module is imported
# This will create a client for the main event loop
import asyncio
loop = asyncio.get_event_loop()
database = loop.run_until_complete(initialize_database())

async def get_database() -> AsyncIOMotorDatabase:
    """
    Get the MongoDB database for the current event loop.

    This function ensures that the database is initialized for the current event loop.
    It should be called at the beginning of any function that needs to access the database.

    Returns:
        AsyncIOMotorDatabase: The MongoDB database instance
    """
    return await initialize_database()

async def release_database() -> None:
    """
    Release the MongoDB client for the current event loop.
    This decrements the reference count and allows the client to be closed
    when it's no longer needed.
    """
    try:
        # Get the current event loop
        current_loop = asyncio.get_running_loop()
        loop_id = id(current_loop)

        # Get the lock for this event loop if it exists
        if loop_id in _locks:
            lock = _locks[loop_id]

            async with lock:
                if loop_id in _client_ref_counts:
                    _client_ref_counts[loop_id] = max(0, _client_ref_counts[loop_id] - 1)
                    ref_count = _client_ref_counts[loop_id]
                    logger.debug(f"Released MongoDB client for event loop {loop_id} (ref count: {ref_count})")

                    # Update last used timestamp
                    _client_last_used[loop_id] = time.time()

                    # If reference count is 0, we could close the client, but we'll leave that to the cleanup job
                    # to avoid closing it if it might be needed again soon
    except RuntimeError:
        # No running event loop
        pass
    except Exception as e:
        logger.error(f"Error releasing MongoDB client: {str(e)}")

async def shutdown_db() -> None:
    """
    Properly close all database connections.
    This function should be called during application shutdown.
    """
    logger.info("Shutting down database connections")
    await close_all_clients(force=True)
    logger.info("All database connections closed")

'''
This file contains the controller for the companies API.
It is used to get the companies, sectors, and industry groups.
'''
from typing import List
from fastapi import APIRouter, HTTPException
from app.services.company_service import (
    get_sp_sl20_companies,
    get_all_companies,
    get_company_sectors,
    get_all_sectors,
    get_industry_groups,
    get_companies_by_industry_groups
)
from app.models.companies_models import (
    CompaniesResponse,
    SectorsResponse,
    AllSectorsResponse,
    IndustryGroupsResponse,
    CompaniesByIndustryResponse
)

router = APIRouter(tags=["Companies"])
# -------------GET REQUESTS-------------
@router.get("/api/companies",
            summary="Get available companies",
            description="Returns a list of all available companies or S&PSL20 companies.",
            response_description="Dictionary mapping company names to ticker symbols",
            response_model=CompaniesResponse)
async def get_companies(sp_sl20_only: bool = False):
    """
    Get a list of available companies for portfolio creation.

    Args:
        sp_sl20_only: If True, only returns companies in the S&PSL20 index

    Returns:
        Dictionary mapping company names to ticker symbols

    Raises:
        HTTPException: If company retrieval fails
    """
    try:
        if sp_sl20_only:
            # Get S&PSL20 companies
            companies = await get_sp_sl20_companies()
        else:
            # Get all companies
            companies = await get_all_companies()

        return {
            "companies": companies
        }

    except Exception as e:
        # Handle errors
        raise HTTPException(status_code=500,
                            detail=f"An error occurred while retrieving companies: {str(e)}") from e

@router.post("/api/sectors",
            summary="Get company sectors for specific tickers",
            description="Returns a mapping of ticker symbols to their sectors for the provided tickers.",
            response_description="Dictionary mapping ticker symbols to sectors",
            response_model=SectorsResponse)
async def get_sectors_by_tickers(ticker_symbols: List[str]):
    """
    Get the sector for each of the specified companies.

    Args:
        ticker_symbols: List of ticker symbols to get sectors for

    Returns:
        Dictionary mapping ticker symbols to sectors

    Raises:
        HTTPException: If sector retrieval fails
    """
    try:
        # Validate input
        if not ticker_symbols:
            return {"sectors": {}}

        # Get company sectors for the specified tickers
        sectors = await get_company_sectors(ticker_symbols)

        return {"sectors": sectors}

    except Exception as e:
        # Handle errors
        raise HTTPException(status_code=500,
                            detail=f"An error occurred while retrieving sectors: {str(e)}") from e

@router.get("/api/all-sectors",
            summary="Get all sectors",
            description="Returns a list of all unique sectors available in the database.",
            response_description="List of sector names",
            response_model=AllSectorsResponse)
async def get_all_sectors_endpoint():
    """
    Get a list of all unique sectors available in the database.

    Returns:
        List of sector names

    Raises:
        HTTPException: If sector retrieval fails
    """
    try:
        # Get all sectors
        sectors = await get_all_sectors()

        return {"sectors": sectors}

    except Exception as e:
        # Handle errors
        raise HTTPException(status_code=500,
                            detail=f"An error occurred while retrieving sectors: {str(e)}") from e


@router.get("/api/industry-groups",
            summary="Get industry groups",
            description="Returns a list of all available industry groups.",
            response_description="List of industry group names",
            response_model=IndustryGroupsResponse)
async def get_industry_groups_endpoint():
    """
    Get a list of all available industry groups.

    Returns:
        List of industry group names

    Raises:
        HTTPException: If industry group retrieval fails
    """
    try:
        # Get industry groups
        industry_groups = await get_industry_groups()

        return {"industry_groups": industry_groups}

    except Exception as e:
        # Handle errors
        raise HTTPException(status_code=500,
                            detail=f"An error occurred while retrieving industry groups: {str(e)}") from e


# -------------POST REQUESTS-------------

@router.post("/api/companies/by-industry",
            summary="Get companies by industry groups",
            description="Returns companies grouped by the specified industry groups.",
            response_description="Dictionary mapping industry groups to companies",
            response_model=CompaniesByIndustryResponse)
async def get_companies_by_industry(industry_groups: List[str]):
    """
    Get companies grouped by the specified industry groups.

    Args:
        industry_groups: List of industry group names to retrieve companies for

    Returns:
        Dictionary mapping industry groups to dictionaries of company names and ticker symbols

    Raises:
        HTTPException: If company retrieval fails
    """
    try:
        # Validate input
        if not industry_groups:
            raise ValueError("At least one industry group must be specified")

        # Get companies by industry groups
        companies = await get_companies_by_industry_groups(industry_groups)

        # Count total companies
        total_companies = sum(len(group) for group in companies.values())

        # Check if we have enough companies for optimization
        if total_companies < 5:
            return {
                "companies": companies,
                "warning": '''Less than 5 companies selected.
                For optimal portfolio diversification, select at least 5 companies.'''}

        return {"companies": companies}

    except ValueError as ve:
        # Handle validation errors
        raise HTTPException(status_code=400, detail=str(ve)) from ve
    except Exception as e:
        # Handle general errors
        raise HTTPException(status_code=500, detail=f"An error occurred while retrieving companies: {str(e)}") from e
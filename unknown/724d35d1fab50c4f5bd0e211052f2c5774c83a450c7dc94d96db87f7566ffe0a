import time
from typing import Dict
from fastapi import <PERSON><PERSON><PERSON><PERSON>x<PERSON>, Depends
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
import httpx
from jose import jwt

from app.config import settings

security = HTTPBearer()

# Cache the JWKS for better performance
JWKS_CACHE = None
JWKS_LAST_FETCHED = None

async def get_clerk_jwks():
    global JWKS_CACHE, JWKS_LAST_FETCHED

    # Simple caching to avoid frequent requests to Clerk
    current_time = time.time()
    if JWKS_CACHE is None or JWKS_LAST_FETCHED is None or current_time - JWKS_LAST_FETCHED > 3600:
        async with httpx.AsyncClient() as client:
            response = await client.get(f"{settings.CLERK_API_URL}/.well-known/jwks.json")
            response.raise_for_status()
            JWKS_CACHE = response.json()
            JWKS_LAST_FETCHED = current_time

    return JWKS_CACHE

async def verify_auth_token(credentials: HTTPAuthorizationCredentials = Depends(security)) -> Dict:
    token = credentials.credentials

    try:
        # For production, use proper JWT verification with jwks
        # This is a simplified version
        payload = jwt.decode(
            token,
            settings.CLERK_JWT_KEY,  # In production, use the JW<PERSON> from Clerk's JWKS endpoint
            algorithms=["RS256"],
            audience="your-audience",  # Configure this in Clerk
            options={"verify_signature": False} if not settings.CLERK_JWT_KEY else None,
        )

        # Optionally verify with Clerk's API
        # async with httpx.AsyncClient() as client:
        #     response = await client.get(
        #         f"{settings.CLERK_API_URL}/users/{payload['sub']}",
        #         headers={"Authorization": f"Bearer {settings.CLERK_SECRET_KEY}"}
        #     )
        #     if response.status_code != 200:
        #         raise HTTPException(status_code=401, detail="Invalid token")

        return payload
    except Exception as e:
        raise HTTPException(status_code=401, detail=f"Invalid authentication credentials: {str(e)}")
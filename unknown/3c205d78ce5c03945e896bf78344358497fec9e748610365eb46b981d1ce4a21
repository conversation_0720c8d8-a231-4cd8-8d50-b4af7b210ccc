"""
User Controller

This module provides endpoints for user profile management and admin functionality.
"""

from typing import Dict, Any, Optional
import logging
from pydantic import BaseModel
from fastapi import Request, HTTPException, APIRouter, Depends
from app.services.user_service import UserService

# Configure logging and routers
logger = logging.getLogger(__name__)
router = APIRouter(prefix="/api/user", tags=["user"])
admin_router = APIRouter(prefix="/api/admin", tags=["admin"])

class ProfileUpdate(BaseModel):
    """
    Profile update model
    """
    bio: Optional[str] = None
    location: Optional[str] = None
    website: Optional[str] = None
    avatar_url: Optional[str] = None
    # Add other custom profile fields as needed

# Function to verify auth token (placeholder - implement your actual auth logic)
async def verify_auth_token(request: Request) -> Dict[str, Any]:
    '''
    Verify the auth token
    '''
    # Implement your token verification logic here
    # This is a placeholder - replace with your actual implementation
    auth_header = request.headers.get("Authorization")
    if not auth_header or not auth_header.startswith("Bearer "):
        raise HTTPException(status_code=401, detail="Invalid or missing token")

    # Extract and verify token
    # Return user data from token
    # For now, returning dummy data
    return {
        "sub": "user_123",  # clerk_user_id
        "email": "<EMAIL>",
        "first_name": "John",
        "last_name": "Doe"
    }

# User profile routes
@router.get("/profile")
async def get_user_profile(auth_data: Dict[str, Any] = Depends(verify_auth_token)):
    """Get the user's profile from MongoDB"""
    try:
        clerk_user_id = auth_data.get("sub")
        if not clerk_user_id:
            raise HTTPException(status_code=401, detail="Invalid user ID in token")

        user = await UserService.get_or_create_user(clerk_user_id, {
            "email": auth_data.get("email"),
            "first_name": auth_data.get("first_name", ""),
            "last_name": auth_data.get("last_name", "")
        })
        return user
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e)) from e

@router.put("/profile")
async def update_user_profile(
    profile_data: ProfileUpdate,
    auth_data: Dict[str, Any] = Depends(verify_auth_token)
):
    """Update the user's profile in MongoDB"""
    try:
        clerk_user_id = auth_data.get("sub")
        if not clerk_user_id:
            raise HTTPException(status_code=401, detail="Invalid user ID in token")

        updated_user = await UserService.update_user_profile(
            clerk_user_id,
            profile_data.model_dump(exclude_unset=True)
        )
        return updated_user
    except ValueError as e:
        raise HTTPException(status_code=404, detail=str(e)) from e
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e)) from e

# Admin routes
@admin_router.get("/users")
async def get_users(
    skip: int = 0,
    limit: int = 100
):
    """Get all users (admin only)"""
    # NOTE: In a real app, you'd check if the user is an admin here
    try:
        # Implement get_all_users method in UserService
        users = await UserService.get_all_users(skip, limit)
        return users
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e)) from e

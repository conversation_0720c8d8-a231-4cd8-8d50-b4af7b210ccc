# API Documentation

This document provides detailed information about the CSE Portfolio Builder API endpoints, request/response formats, and error handling.

## Table of Contents

1. [Authentication](#authentication)
2. [Response Formats](#response-formats)
3. [Error Handling](#error-handling)
4. [Portfolio Endpoints](#portfolio-endpoints)
5. [Company and Industry Endpoints](#company-and-industry-endpoints)
6. [System Endpoints](#system-endpoints)

## Authentication

The API uses token-based authentication. All requests to protected endpoints must include an authentication token in the `Authorization` header.

```
Authorization: Bearer <your_token>
```

## Response Formats

All successful responses follow a consistent format:

```json
{
  "data_field": "response_data",
  "count": 10,
  "message": "Operation successful message"
}
```

- Most endpoints include a `message` field with a human-readable description of the result
- Collection endpoints include a `count` field with the number of items returned
- Some endpoints may include additional metadata fields

## Error Handling

### Error Response Format

All error responses follow this standard format:

```json
{
  "detail": {
    "detail": "Error message describing what went wrong",
    "error_code": "ERROR_CODE_FOR_PROGRAMMATIC_HANDLING",
    "status_code": 400,
    "errors": [
      {
        "loc": ["body", "field_name"],
        "msg": "Specific validation error message",
        "type": "error_type"
      }
    ]
  }
}
```

- `detail`: Human-readable error message
- `error_code`: Machine-readable error code for programmatic handling
- `status_code`: HTTP status code
- `errors`: Optional array of detailed validation errors (for 422 Unprocessable Entity responses)

### Common HTTP Status Codes

- **400 Bad Request**: Invalid input parameters (e.g., invalid portfolio ID format)
- **404 Not Found**: Requested resource not found (e.g., portfolio not found)
- **422 Unprocessable Entity**: Request validation failed (e.g., invalid data format)
- **500 Internal Server Error**: Unexpected server error

### Common Error Codes

| Error Code | Description | HTTP Status |
|------------|-------------|-------------|
| `INVALID_PORTFOLIO_ID` | Invalid portfolio ID format | 400 |
| `PORTFOLIO_NOT_FOUND` | Portfolio not found | 404 |
| `VALIDATION_ERROR` | Input validation failed | 422 |
| `MISSING_TICKER_SYMBOLS` | No ticker symbols provided | 400 |
| `SECTORS_NOT_FOUND` | No sectors found for the specified companies | 404 |
| `INDUSTRY_GROUPS_NOT_FOUND` | No industry groups found | 404 |
| `PORTFOLIO_UPDATE_ERROR` | Error updating portfolio | 500 |
| `PORTFOLIO_DELETION_ERROR` | Error deleting portfolio | 500 |
| `PORTFOLIO_RETRIEVAL_ERROR` | Error retrieving portfolio | 500 |
| `COMPANY_RETRIEVAL_ERROR` | Error retrieving companies | 500 |
| `SECTOR_RETRIEVAL_ERROR` | Error retrieving sectors | 500 |

## Portfolio Endpoints

### Get All Portfolios

```
GET /api/portfolios
```

Returns a list of all portfolios.

**Response:**

```json
{
  "portfolios": [
    {
      "portfolio_id": "PF-12345",
      "portfolio_name": "My Portfolio",
      "portfolio_type": "CUSTOM",
      "total_investment_amount": 1000000.0,
      "total_market_value": 1050000.0,
      "unrealized_gain_loss": 50000.0,
      "annualized_return": 0.15,
      "annualized_volatility": 0.2,
      "creation_date": "2023-01-01T00:00:00",
      "rebalance_frequency": "QUARTERLY",
      "last_rebalanced_date": "2023-03-01T00:00:00",
      "last_updated_date": "2023-04-01T00:00:00",
      "holdings_count": 10
    }
  ]
}
```

**Possible Errors:**

- `500 PORTFOLIO_RETRIEVAL_ERROR`: Error retrieving portfolios

### Get Portfolio Details

```
GET /api/portfolios/{portfolio_id}
```

Returns details of a specific portfolio.

**Parameters:**

- `portfolio_id`: The ID of the portfolio to retrieve (format: PF-XXXXX)

**Response:**

```json
{
  "portfolio": {
    "portfolio_id": "PF-12345",
    "portfolio_name": "My Portfolio",
    "portfolio_type": "CUSTOM",
    "total_investment_amount": 1000000.0,
    "total_market_value": 1050000.0,
    "unrealized_gain_loss": 50000.0,
    "annualized_return": 0.15,
    "annualized_volatility": 0.2,
    "creation_date": "2023-01-01T00:00:00",
    "rebalance_frequency": "QUARTERLY",
    "last_rebalanced_date": "2023-03-01T00:00:00",
    "last_updated_date": "2023-04-01T00:00:00",
    "holdings": [
      {
        "company_name": "Company A",
        "ticker_symbol": "COMP.N0000",
        "quantity": 100,
        "last_traded_price": 150.0,
        "total_cost": 14000.0,
        "market_value": 15000.0,
        "unrealized_gain_loss": 1000.0,
        "weight": 0.15,
        "annualized_return": 0.12,
        "annualized_volatility": 0.18
      }
    ]
  }
}
```

**Possible Errors:**

- `400 INVALID_PORTFOLIO_ID`: Invalid portfolio ID format
- `404 PORTFOLIO_NOT_FOUND`: Portfolio not found
- `500 PORTFOLIO_RETRIEVAL_ERROR`: Error retrieving portfolio

### Update Portfolio

```
PATCH /api/portfolios/{portfolio_id}
```

Updates a specific portfolio with the latest market data.

**Parameters:**

- `portfolio_id`: The ID of the portfolio to update (format: PF-XXXXX)

**Response:**

```json
{
  "success": true,
  "message": "Portfolio updated successfully",
  "portfolio_id": "PF-12345",
  "last_updated_date": "2023-04-01T00:00:00",
  "total_market_value": 1050000.0,
  "total_unrealized_gain_loss": 50000.0,
  "holdings_updated": 10
}
```

**Possible Errors:**

- `400 INVALID_PORTFOLIO_ID`: Invalid portfolio ID format
- `404 PORTFOLIO_NOT_FOUND`: Portfolio not found
- `500 PORTFOLIO_UPDATE_ERROR`: Error updating portfolio

### Delete Portfolio

```
DELETE /api/portfolios/{portfolio_id}
```

Permanently deletes a portfolio by ID.

**Parameters:**

- `portfolio_id`: The ID of the portfolio to delete (format: PF-XXXXX)

**Response:**

```json
{
  "success": true,
  "message": "Portfolio deleted successfully",
  "portfolio_id": "PF-12345"
}
```

**Possible Errors:**

- `400 INVALID_PORTFOLIO_ID`: Invalid portfolio ID format
- `404 PORTFOLIO_NOT_FOUND`: Portfolio not found
- `500 PORTFOLIO_DELETION_ERROR`: Error deleting portfolio

## Company and Industry Endpoints

### Get All Sectors

```
GET /api/portfolio/all-sectors
```

Returns a list of all unique sectors available in the database.

**Response:**

```json
{
  "sectors": [
    "CONSUMER DISCRETIONARY",
    "CONSUMER STAPLES",
    "ENERGY",
    "FINANCIALS",
    "HEALTH CARE",
    "INDUSTRIALS",
    "MATERIALS",
    "REAL ESTATE",
    "TELECOMMUNICATION SERVICES",
    "UTILITIES"
  ],
  "count": 10,
  "message": "Successfully retrieved 10 sectors"
}
```

**Possible Errors:**

- `404 SECTORS_NOT_FOUND`: No sectors found in the database
- `500 SECTOR_RETRIEVAL_ERROR`: Error retrieving sectors

### Get Sectors for Specific Companies

```
POST /api/portfolio/sectors
```

Returns a mapping of ticker symbols to their sectors for the specified companies.

**Request Body:**

```json
{
  "ticker_symbols": [
    "LOLC.N0000",
    "JKH.N0000",
    "COMB.N0000",
    "DIAL.N0000",
    "CCS.N0000"
  ]
}
```

**Response:**

```json
{
  "sectors": {
    "JKH.N0000": "INDUSTRIALS",
    "LOLC.N0000": "FINANCIALS",
    "COMB.N0000": "FINANCIALS",
    "CCS.N0000": "CONSUMER STAPLES",
    "DIAL.N0000": "TELECOMMUNICATION SERVICES"
  },
  "count": 5,
  "message": "Successfully retrieved sectors for 5 companies"
}
```

**Possible Errors:**

- `400 MISSING_TICKER_SYMBOLS`: No ticker symbols provided
- `404 SECTORS_NOT_FOUND`: No sectors found for the specified companies
- `500 SECTOR_RETRIEVAL_ERROR`: Error retrieving sectors

### Get All Industry Groups

```
GET /api/portfolio/industry-groups
```

Returns a list of all available industry groups.

**Response:**

```json
{
  "industry_groups": [
    "S&P/CSE AUTOMOBILES & COMPONENTS",
    "S&P/CSE BANKS",
    "S&P/CSE CAPITAL GOODS"
  ],
  "count": 3,
  "message": "Successfully retrieved 3 industry groups"
}
```

**Possible Errors:**

- `404 INDUSTRY_GROUPS_NOT_FOUND`: No industry groups found in the database
- `500 INDUSTRY_GROUP_RETRIEVAL_ERROR`: Error retrieving industry groups

### Get Companies by Industry Groups

```
POST /api/portfolio/companies-by-industry
```

Returns companies grouped by the specified industry groups.

**Request Body:**

```json
{
  "industry_groups": [
    "S&P/CSE AUTOMOBILES & COMPONENTS",
    "S&P/CSE BANKS",
    "S&P/CSE CAPITAL GOODS"
  ]
}
```

**Response:**

```json
{
  "companies": {
    "S&P/CSE BANKS": {
      "DFCC BANK PLC": "DFCC.N0000",
      "SAMPATH BANK PLC": "SAMP.N0000"
    },
    "S&P/CSE CAPITAL GOODS": {
      "JOHN KEELLS HOLDINGS PLC": "JKH.N0000",
      "ROYAL CERAMICS LANKA PLC": "RCL.N0000"
    }
  },
  "count": 4,
  "industry_groups_count": 2,
  "message": "Successfully retrieved 4 companies across 2 industry groups"
}
```

**Possible Errors:**

- `400 MISSING_INDUSTRY_GROUPS`: No industry groups provided
- `404 INDUSTRY_GROUPS_NOT_FOUND`: None of the requested industry groups were found
- `500 COMPANY_RETRIEVAL_ERROR`: Error retrieving companies

## System Endpoints

### Health Check

```
GET /health
```

Returns the health status of the API.

**Response:**

```json
{
  "status": "healthy",
  "version": "1.0.0",
  "uptime": "10h 30m 15s",
  "database_connection": "connected"
}
```

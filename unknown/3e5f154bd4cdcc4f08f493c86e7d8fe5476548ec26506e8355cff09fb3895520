# Backend - CSE Portfolio Builder

This directory contains the FastAPI backend for the Minimum Variance Portfolio Builder application, which handles all server-side logic for portfolio optimization, data management, and external API integrations.

## Tech Stack

- **Python** 3.12+: The core programming language used for backend development
- **FastAPI**: A modern, high-performance web framework for building APIs with Python
- **Poetry**: Dependency management and packaging tool that simplifies library versioning
- **MongoDB**: NoSQL database for storing portfolio data, user information, and historical market data
- **Pandas** and **NumPy**: Libraries for efficient data manipulation and numerical calculations
- **SciKit-Learn**: Machine learning library used for portfolio optimization algorithms
- **Plotly**: Interactive visualization library for creating portfolio performance charts
- **APScheduler**: Advanced Python scheduler for running periodic tasks like data updates
- **Clerk**: Authentication service for secure user management

## Environment Setup

### Environment Variables

The application relies on environment variables for configuration settings, API keys, and connection strings. These are stored in a `.env` file which is excluded from version control for security purposes.

A template file named `.env.example` is provided in the repository with placeholders for all required variables. To set up your environment:

1. Copy the `.env.example` file to a new file named `.env`
2. Replace all placeholder values with your actual configuration values
3. Ensure the `.env` file is in the root directory of the project

Example `.env` file structure:

```env
# MongoDB Configuration
MONGO_URI=mongodb+srv://username:<EMAIL>/?retryWrites=true&w=majority
DATABASE_NAME=your_database_name

# CSE API Configuration
CSE_API_HISTORICAL_DATA_URL=https://www.cse.lk/api/historicalTrades
CSE_API_DAILY_SUMMARY_DATA_URL=https://www.cse.lk/api/tradeSummary
CSE_API_TOKEN_URL=https://www.cse.lk/api/signInNew
CSE_API_USERNAME=<EMAIL>
CSE_API_PASSWORD=your_password

# Clerk Authentication
CLERK_SECRET_KEY=sk_test_your_clerk_secret_key
CLERK_JWT_KEY=your_clerk_jwt_verification_key
CLERK_WEBHOOK_SIGNING_SECRET=whsec_your_signing_secret

# Environment Settings
PORT=8000
API_BASE_URL=http://localhost:8000
```

### Installation

1. **Install Python 3.12+**

   - Download from [python.org](https://www.python.org/downloads/) or use a version manager like pyenv
   - Verify installation with: `python --version`

2. **Install Poetry**

   - Follow the official installation guide: [https://python-poetry.org/docs/#installation](https://python-poetry.org/docs/#installation)
   - For macOS/Linux users: `curl -sSL https://install.python-poetry.org | python3 -`
   - For Windows users: `(Invoke-WebRequest -Uri https://install.python-poetry.org -UseBasicParsing).Content | python -`
   - Verify installation with: `poetry --version`

3. **Install Project Dependencies**

   - Navigate to the project root directory
   - Run: `poetry install`
   - This will create a virtual environment and install all dependencies defined in `pyproject.toml`

4. **Configure Environment**
   - Set up your `.env` file as described in the previous section
   - Ensure MongoDB is running and accessible via the connection string specified in your `.env` file

### Running the Application

To start the FastAPI server in development mode:

```bash
poetry run uvicorn app.main:app --reload
```

This command:

- Activates the poetry virtual environment
- Starts the uvicorn ASGI server
- Loads the FastAPI application instance from `app.main:app`
- Enables auto-reload for development (changes to the code will restart the server automatically)

Once running, you can access:

- The API at [http://localhost:8000](http://localhost:8000)
- Interactive API documentation at [http://localhost:8000/docs](http://localhost:8000/docs)
- Alternative documentation UI at [http://localhost:8000/redoc](http://localhost:8000/redoc)

### Development with Webhooks

When working with webhooks during development, there's a challenge because external services cannot directly reach your localhost. To address this:

1. **Install ngrok**: Download from [ngrok.com](https://ngrok.com/download) and set up an account
2. **Start your FastAPI application** as described above
3. **Create an ngrok tunnel** to expose your local server:

   ```bash
   ngrok http 8000
   ```

4. **Use the provided ngrok URL** (e.g., `https://1a2b3c4d5e.ngrok.io`) as the webhook endpoint in your external service dashboards
5. **Update your webhook configuration** in the Clerk dashboard (or other services) to use this URL

This allows external services to send webhook events to your local development environment through the ngrok proxy.

## API Endpoints

The backend exposes several key endpoints:

### Portfolio Endpoints

- **POST** `/api/portfolio/create` - Create a portfolio (Min Variance, Max Sharpe, or Custom)
- **POST** `/api/portfolio/analyze` - Analyze and optimize a custom portfolio
- **GET** `/api/portfolios` - Get all portfolios
- **GET** `/api/portfolios/{portfolio_id}` - Get a specific portfolio by ID
- **PATCH** `/api/portfolios/{portfolio_id}` - Update a specific portfolio with latest market data
- **PATCH** `/api/portfolios` - Update all portfolios with latest market data
- **DELETE** `/api/portfolios/{portfolio_id}` - Delete a portfolio by ID
- **GET** `/api/portfolio/companies` - Get available companies (all or S&PSL20 only)
- **GET** `/api/portfolio/industry-groups` - Get available industry groups
- **GET** `/api/portfolio/all-sectors` - Get all available sectors
- **POST** `/api/portfolio/sectors` - Get sectors for specific companies
- **POST** `/api/portfolio/companies-by-industry` - Get companies by industry groups

### System Endpoints

- **GET** `/health` - Health check endpoint
- **GET** `/config` - View current configuration (with sensitive information masked)
- **GET** `/fetch-daily-trades` - Fetch daily trade summary data
- **GET** `/test-scheduler` - Test the scheduler functionality

For a complete list of endpoints with detailed request/response schemas, refer to:

- Interactive API documentation at `/docs` when the server is running
- Detailed API documentation in the [docs/API.md](docs/API.md) file

## Error Handling

The API implements a standardized error handling mechanism to provide consistent and informative error responses:

### Error Response Format

All error responses follow this standard format:

```json
{
  "detail": {
    "detail": "Error message describing what went wrong",
    "error_code": "ERROR_CODE_FOR_PROGRAMMATIC_HANDLING",
    "status_code": 400,
    "errors": [
      {
        "loc": ["body", "field_name"],
        "msg": "Specific validation error message",
        "type": "error_type"
      }
    ]
  }
}
```

- `detail`: Human-readable error message
- `error_code`: Machine-readable error code for programmatic handling
- `status_code`: HTTP status code
- `errors`: Optional array of detailed validation errors (for 422 Unprocessable Entity responses)

### Common HTTP Status Codes

- **400 Bad Request**: Invalid input parameters (e.g., invalid portfolio ID format)
- **404 Not Found**: Requested resource not found (e.g., portfolio not found)
- **422 Unprocessable Entity**: Request validation failed (e.g., invalid data format)
- **500 Internal Server Error**: Unexpected server error

### Common Error Codes

- `INVALID_PORTFOLIO_ID`: Invalid portfolio ID format
- `PORTFOLIO_NOT_FOUND`: Portfolio not found
- `VALIDATION_ERROR`: Input validation failed
- `MISSING_TICKER_SYMBOLS`: No ticker symbols provided
- `SECTORS_NOT_FOUND`: No sectors found for the specified companies
- `INDUSTRY_GROUPS_NOT_FOUND`: No industry groups found
- `PORTFOLIO_UPDATE_ERROR`: Error updating portfolio
- `PORTFOLIO_DELETION_ERROR`: Error deleting portfolio
- `PORTFOLIO_RETRIEVAL_ERROR`: Error retrieving portfolio
- `COMPANY_RETRIEVAL_ERROR`: Error retrieving companies
- `SECTOR_RETRIEVAL_ERROR`: Error retrieving sectors

### Error Handling Best Practices

When working with the API:

1. Always check the HTTP status code first
2. For 4xx errors, check the `error_code` to determine the specific error
3. For validation errors (422), check the `errors` array for field-specific errors
4. Log the full error response for debugging purposes

## Testing the API

For comprehensive API testing:

1. **Authenticated Testing via Frontend**:

   - Sign in through the frontend application
   - Use the same browser session to access the `/docs` interactive documentation
   - This preserves authentication cookies/tokens for testing secured endpoints

2. **Direct Testing (Development Only)**:

   - Temporarily modify the `app/middleware.py` file to bypass authentication
   - Add a condition to skip authentication for local requests or when a specific header is present
   - **Note**: Always restore authentication before deploying to production

3. **Automated Testing**:
   - Run the test suite with: `poetry run pytest`
   - Integration tests are in the `tests/` directory
   - Mock authentication is provided for test scenarios

4. **Example Scripts**:
   - Check the `examples/` directory for example scripts that demonstrate API usage
   - The `error_handling_example.py` script shows how to handle errors from the API
   - See [examples/README.md](examples/README.md) for more information

## Data Flow Architecture

1. **User Request** → FastAPI endpoints receive client requests
2. **Authentication** → Requests are verified through middleware
3. **Data Processing** → Pandas/NumPy process financial data
4. **Optimization** → SciKit-Learn algorithms calculate optimal portfolios
5. **Database Operations** → Results stored in MongoDB
6. **Response** → Optimized data returned to client

For detailed API specifications, refer to the interactive documentation available at `/docs` when the server is running.

## Utility Scripts

The project includes several utility scripts in the `scripts/` directory:

- **cleanup.py** - Removes `__pycache__` directories and `.pyc` files
- **run_tests.py** - Runs all tests in the `tests/` directory
- **cse_historical_trades.py** - Fetches and stores historical trade data (for database maintenance)

To run a script:

```bash
python scripts/script_name.py
```

Note: On Windows, you may need to run the cleanup script with administrator privileges to remove `__pycache__` directories.

## Statistical Refinements

The portfolio optimization engine includes advanced statistical techniques to improve the robustness of the results:

### Winsorization

- Automatically applies 2.5% winsorization to all returns data
- Limits the influence of outliers by capping extreme values at the 2.5th and 97.5th percentiles
- Improves the stability of optimization results by reducing the impact of market anomalies
- Implemented using `scipy.stats.mstats.winsorize`

### Ledoit-Wolf Covariance Shrinkage

- Applies Ledoit-Wolf shrinkage to the covariance matrix
- Reduces estimation error in the covariance matrix, especially with limited historical data
- Improves the conditioning of the matrix, making optimization more stable
- Automatically determines the optimal shrinkage intensity
- Implemented using `sklearn.covariance.LedoitWolf`

These refinements are applied automatically and are not configurable by end users, ensuring consistent and robust portfolio optimization results.

## Recent Improvements

The system has been enhanced with several improvements:

### API Enhancements

- **RESTful API Design**: Improved API design following RESTful principles
- **PATCH for Updates**: Using PATCH method for partial updates to portfolios
- **Standardized Responses**: Consistent response formats with detailed information
- **Improved Documentation**: More detailed API documentation with examples
- **Hard Delete Support**: Added support for permanently deleting portfolios
- **Robust Error Handling**: Standardized error responses with appropriate HTTP status codes and error codes
- **Input Validation**: Enhanced validation for all API inputs with clear error messages

### Portfolio Creation and Storage

- **Integer Quantities**: All stock quantities are now integers (no fractional shares)
- **Cash Component**: Unallocated funds are tracked as a cash component
- **Market Value Matching**: Total market value equals total investment amount at creation
- **Improved Validation**: Better validation for weights, investment amounts, and target returns
- **Daily Updates**: Automatic daily updates of portfolio market values after market close

### Performance Optimizations

- **Batch Database Queries**: Optimized database operations by fetching data in batches
- **Data Caching**: Reduced database queries by caching frequently used data
- **Efficient Allocation Algorithm**: Two-pass algorithm for calculating integer quantities and distributing remaining funds
- **Precise Calculations**: Improved precision in financial calculations with proper rounding

### Code Quality

- **Improved Error Handling**: More detailed error messages and better exception handling
- **Enhanced Logging**: More context in log messages for better debugging
- **Code Organization**: Better separation of concerns and more consistent code structure
- **Removed Deprecated Code**: Cleaned up deprecated endpoints and functions

## Project Architecture

The project follows a layered architecture with feature modularity:

### Directory Structure

- **app/** - Main application code
  - **controllers/** - API endpoints and controllers
  - **core/** - Core business logic and optimization engine
  - **db/** - Database access and repository layer
  - **models/** - Data models and schemas
  - **services/** - Service layer for business operations
  - **utils/** - Utility functions and helpers
- **docs/** - Detailed API documentation
- **examples/** - Example scripts demonstrating API usage
- **scripts/** - Standalone scripts for database population
- **tests/** - Test scripts and visualization tools

### Layers

1. **API Layer** - Handles HTTP requests and responses
2. **Service Layer** - Implements business logic and orchestrates operations
3. **Core/Engine Layer** - Contains the portfolio optimization algorithms
4. **Data Access Layer** - Manages database interactions
5. **Configuration Layer** - Handles application settings
6. **Models/Schemas Layer** - Defines data structures

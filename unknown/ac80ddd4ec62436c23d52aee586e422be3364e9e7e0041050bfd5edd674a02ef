# MongoDB Configuration
MONGO_URI=mongodb+srv://username:<EMAIL>/?retryWrites=true&w=majority
DATABASE_NAME=your_database_name

# CSE API Configuration
CSE_API_HISTORICAL_DATA_URL=https://www.cse.lk/api/historicalTrades
CSE_API_DAILY_SUMMARY_DATA_URL=https://www.cse.lk/api/tradeSummary
CSE_API_TOKEN_URL=https://www.cse.lk/api/signInNew
CSE_API_ASPI_DATA_URL=https://www.cse.lk/api/aspiData
CSE_API_SNPSL_DATA_URL=https://www.cse.lk/api/snpData
CSE_API_USERNAME=<EMAIL>
CSE_API_PASSWORD=your_password

# Clerk Authentication
CLERK_SECRET_KEY=sk_test_your_clerk_secret_key
CLERK_JWT_KEY=your_clerk_jwt_verification_key
CLERK_WEBHOOK_SIGNING_SECRET=whsec_your_signing_secret

# Environment Settings
PORT=8000
API_BASE_URL=http://localhost:8000

'''
This file contains the service for the user.
It is used to get the user from the database.
'''

from typing import Dict, Optional, Any, cast
from datetime import datetime, timezone
from motor.motor_asyncio import AsyncIOMotorCollection
from app.db.session import database
from app.utils.db_utils import safe_mongodb_operation

# Initialize users_collection only if database is not None
users_collection: Optional[AsyncIOMotorCollection] = None
if database is not None:
    users_collection = database["users"]

class UserService:
    '''
    This class contains the service for the user.
    It is used to get the user from the database.
    '''
    @staticmethod
    def _convert_mongodb_doc(doc: Optional[Dict[str, Any]]) -> Dict[str, Any]:
        """Convert MongoDB document to a JSON-serializable dictionary"""
        if not doc:
            return {}
        result = dict(doc)
        if "_id" in result:
            result["_id"] = str(result["_id"])
        return result

    @staticmethod
    async def get_or_create_user(clerk_user_id: str, user_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Get a user from MongoDB by Clerk ID or create if not exists
        """
        if users_collection is None:
            return {}

        # Find existing user
        user_doc = await safe_mongodb_operation(
            lambda: users_collection.find_one({"clerk_user_id": clerk_user_id}) if users_collection is not None else None,
            fallback_value=None
        )
        user_doc = cast(Optional[Dict[str, Any]], user_doc)

        if not user_doc:
            # Create new user in your MongoDB
            insert_result = await safe_mongodb_operation(
                lambda: users_collection.insert_one({
                    "_id": clerk_user_id,  # Use clerk_user_id as MongoDB _id
                    "clerk_user_id": clerk_user_id,
                    "email": user_data.get("email"),
                    "first_name": user_data.get("first_name"),
                    "last_name": user_data.get("last_name"),
                    "portfolios": [],
                    "created_at": datetime.now(timezone.utc),
                    "last_updated": datetime.now(timezone.utc)
                }) if users_collection is not None else None,
                fallback_value=None
            )
            if insert_result:
                user_doc = await safe_mongodb_operation(
                    lambda: users_collection.find_one({"clerk_user_id": clerk_user_id}) if users_collection is not None else None,
                    fallback_value=None
                )
                user_doc = cast(Optional[Dict[str, Any]], user_doc)

        return UserService._convert_mongodb_doc(user_doc)

    @staticmethod
    async def update_user_profile(clerk_user_id: str, profile_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Update user profile in MongoDB
        """
        if users_collection is None:
            return {}

        update_result = await safe_mongodb_operation(
            lambda: users_collection.update_one(
                {"clerk_user_id": clerk_user_id},
                {
                    "$set": {
                        "profile": profile_data,
                        "last_updated": datetime.now(timezone.utc)
                    }
                }
            ) if users_collection is not None else None,
            fallback_value=None
        )

        if not update_result or not getattr(update_result, 'matched_count', 0):
            raise ValueError("User not found")

        user_doc = await safe_mongodb_operation(
            lambda: users_collection.find_one({"clerk_user_id": clerk_user_id}) if users_collection is not None else None,
            fallback_value=None
        )
        user_doc = cast(Optional[Dict[str, Any]], user_doc)

        return UserService._convert_mongodb_doc(user_doc)

    @staticmethod
    async def update_user_basic_info(clerk_user_id: str, user_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Update basic user information in MongoDB (from webhook)
        """
        if users_collection is None:
            return {}

        update_data = {
            "last_updated": datetime.now(timezone.utc)
        }

        # Only include fields that are present in user_data
        if "email" in user_data and user_data["email"]:
            update_data["email"] = user_data["email"]
        if "first_name" in user_data:
            update_data["first_name"] = user_data["first_name"]
        if "last_name" in user_data:
            update_data["last_name"] = user_data["last_name"]
        if "last_login" in user_data:
            update_data["last_login"] = datetime.fromisoformat(user_data["last_login"]) if isinstance(user_data["last_login"], str) else user_data["last_login"]

        # Check if user exists first
        user_doc = await safe_mongodb_operation(
            lambda: users_collection.find_one({"clerk_user_id": clerk_user_id}) if users_collection is not None else None,
            fallback_value=None
        )
        user_doc = cast(Optional[Dict[str, Any]], user_doc)

        if not user_doc:
            # Create new user if not exists
            insert_result = await safe_mongodb_operation(
                lambda: users_collection.insert_one({
                    "_id": clerk_user_id,  # Use clerk_user_id as MongoDB _id
                    "clerk_user_id": clerk_user_id,
                    "email": user_data.get("email"),
                    "first_name": user_data.get("first_name"),
                    "last_name": user_data.get("last_name"),
                    "profile": {},
                    "created_at": datetime.now(timezone.utc),
                    "last_updated": datetime.now(timezone.utc),
                    "last_login": datetime.fromisoformat(user_data["last_login"]) if "last_login" in user_data and isinstance(user_data["last_login"], str) else user_data.get("last_login")
                }) if users_collection is not None else None,
                fallback_value=None
            )
            if insert_result:
                user_doc = await safe_mongodb_operation(
                    lambda: users_collection.find_one({"clerk_user_id": clerk_user_id}) if users_collection is not None else None,
                    fallback_value=None
                )
                user_doc = cast(Optional[Dict[str, Any]], user_doc)
        else:
            # Update existing user
            update_result = await safe_mongodb_operation(
                lambda: users_collection.update_one(
                    {"clerk_user_id": clerk_user_id},
                    {"$set": update_data}
                ) if users_collection is not None else None,
                fallback_value=None
            )
            if not update_result or not getattr(update_result, 'matched_count', 0):
                raise ValueError("User not found")
            user_doc = await safe_mongodb_operation(
                lambda: users_collection.find_one({"clerk_user_id": clerk_user_id}) if users_collection is not None else None,
                fallback_value=None
            )
            user_doc = cast(Optional[Dict[str, Any]], user_doc)

        return UserService._convert_mongodb_doc(user_doc)

    @staticmethod
    async def delete_user(clerk_user_id: str) -> bool:
        """
        Delete user from MongoDB (triggered by webhook)
        """
        if users_collection is None:
            return False

        delete_result = await safe_mongodb_operation(
            lambda: users_collection.delete_one({"clerk_user_id": clerk_user_id}) if users_collection is not None else None,
            fallback_value=None
        )

        return bool(delete_result and getattr(delete_result, 'deleted_count', 0) > 0)  # or result.modified_count > 0 for soft delete

    @staticmethod
    async def get_all_users(skip: int = 0, limit: int = 100) -> list[Dict[str, Any]]:
        """
        Get all users from MongoDB
        """
        if users_collection is None:
            return []

        users = await safe_mongodb_operation(
            lambda: users_collection.find().skip(skip).limit(limit).to_list(length=limit) if users_collection is not None else [],
            fallback_value=[]
        )
        return [UserService._convert_mongodb_doc(user) for user in users]

    @staticmethod
    async def add_portfolio_to_user(clerk_user_id: str, portfolio_id: str) -> bool:
        """
        Add a portfolio to a user. Creates the user if they don't exist.

        Args:
            clerk_user_id: The Clerk user ID
            portfolio_id: The portfolio ID to add

        Returns:
            bool: True if the operation was successful, False otherwise
        """
        if users_collection is None:
            return False

        # First check if user exists
        user_doc = await safe_mongodb_operation(
            lambda: users_collection.find_one({"clerk_user_id": clerk_user_id}) if users_collection is not None else None,
            fallback_value=None
        )
        user_doc = cast(Optional[Dict[str, Any]], user_doc)

        if not user_doc:
            # Create new user if not exists
            insert_result = await safe_mongodb_operation(
                lambda: users_collection.insert_one({
                    "_id": clerk_user_id,  # Use clerk_user_id as MongoDB _id
                    "clerk_user_id": clerk_user_id,
                    "portfolios": [portfolio_id],  # Initialize with the new portfolio
                    "created_at": datetime.now(timezone.utc),
                    "last_updated": datetime.now(timezone.utc)
                }) if users_collection is not None else None,
                fallback_value=None
            )
            return bool(insert_result)
        else:
            # Update existing user
            update_result = await safe_mongodb_operation(
                lambda: users_collection.update_one(
                    {"clerk_user_id": clerk_user_id},
                    {
                        "$push": {"portfolios": portfolio_id},
                        "$set": {"last_updated": datetime.now(timezone.utc)}
                    }
                ) if users_collection is not None else None,
                fallback_value=None
            )
            return bool(update_result and getattr(update_result, 'modified_count', 0) > 0)

    @staticmethod
    async def remove_portfolio_from_user(clerk_user_id: str, portfolio_id: str) -> bool:
        """
        Remove a portfolio from a user
        """
        if users_collection is None:
            return False

        update_result = await safe_mongodb_operation(
            lambda: users_collection.update_one(
                {"clerk_user_id": clerk_user_id},
                {"$pull": {"portfolios": portfolio_id}}
            ) if users_collection is not None else None,
            fallback_value=None
        )

        return bool(update_result and getattr(update_result, 'modified_count', 0) > 0)

    @staticmethod
    async def get_user_portfolios(clerk_user_id: str) -> list[Dict[str, Any]]:
        """
        Get all portfolios for a user
        """
        if users_collection is None:
            return []

        user_doc = await safe_mongodb_operation(
            lambda: users_collection.find_one({"clerk_user_id": clerk_user_id}) if users_collection is not None else None,
            fallback_value=None
        )
        user_doc = cast(Optional[Dict[str, Any]], user_doc)

        return user_doc.get("portfolios", []) if user_doc else []
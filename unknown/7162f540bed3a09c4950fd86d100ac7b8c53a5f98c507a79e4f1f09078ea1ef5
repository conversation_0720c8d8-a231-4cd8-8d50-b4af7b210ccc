'''
This module contains the service for fetching news from the CSE API.
'''

import logging
from datetime import datetime
import httpx
from fastapi import HTTPException
from app.models.news_models import MonthlyNews
from app.config import settings

logger = logging.getLogger(__name__)

async def get_news(query_params: dict) -> MonthlyNews:
    """
    Get the news from the CSE API.

    Args:
        query_params: Dictionary containing query parameters for the API request

    Returns:
        MonthlyNews: Parsed news data

    Raises:
        HTTPException: If there's an error fetching or processing the news data
    """
    try:
        # Set default query parameters
        default_params = {
            "top": "false",
            "year": str(datetime.now().year),
            "type": "CN"
        }

        # Update defaults with provided parameters
        for key, value in query_params.items():
            if value is not None:
                default_params[key] = value

        # Use httpx for async HTTP requests
        async with httpx.AsyncClient(timeout=30.0) as client:
            logger.info("Fetching news with parameters: %s", default_params)
            response = await client.get(settings.CSE_API_NEWS_URL, params=default_params)
            response.raise_for_status()

            # Parse the response into the MonthlyNews model
            news_data = response.json()
            return MonthlyNews.model_validate(news_data)

    except httpx.HTTPStatusError as e:
        error_msg = f"HTTP error when fetching news: {str(e)}"
        logger.error(error_msg)
        raise HTTPException(status_code=e.response.status_code, detail=error_msg) from e
    except Exception as e:
        error_msg = f"Error fetching news data: {str(e)}"
        logger.error(error_msg)
        raise HTTPException(status_code=500, detail=error_msg) from e

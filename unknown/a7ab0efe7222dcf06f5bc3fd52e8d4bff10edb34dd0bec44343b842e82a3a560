"""
Company Service

This module provides functions for retrieving company information from the database.
"""

import logging
from typing import Dict, List, Optional
from app.db.repositories import CompanyRepository

# Configure logging
logger = logging.getLogger(__name__)

async def get_sp_sl20_companies() -> Dict[str, str]:
    """
    Get all companies that are constituents of the S&PSL20 index.

    Returns:
        Dictionary mapping company names to ticker symbols.
        For companies with multiple share types (ORDINARY and NON_VOTING),
        the company name will be appended with the share type in parentheses.
    """
    try:
        # Use the repository to get S&PSL20 companies
        companies_dict = await CompanyRepository.get_sp_sl20_companies()

        logger.info(f"Retrieved {len(companies_dict)} S&PSL20 companies from the database")
        return companies_dict

    except Exception as e:
        logger.error(f"Error retrieving S&PSL20 companies: {str(e)}")
        raise

async def get_all_companies() -> Dict[str, str]:
    """
    Get all companies in the database.

    Returns:
        Dictionary mapping company names to ticker symbols.
        For companies with multiple share types (ORDINARY and NON_VOTING),
        the company name will be appended with the share type in parentheses.
    """
    try:
        # Use the repository to get all companies
        companies_dict = await CompanyRepository.get_all_companies()

        logger.info(f"Retrieved {len(companies_dict)} companies from the database")
        return companies_dict

    except Exception as e:
        logger.error(f"Error retrieving companies: {str(e)}")
        raise

async def get_company_sectors(ticker_symbols: Optional[List[str]] = None) -> Dict[str, str]:
    """
    Get the sector for each company in the database or for specific ticker symbols.

    Args:
        ticker_symbols: Optional list of ticker symbols to get sectors for.
                       If None, returns sectors for all companies.

    Returns:
        Dictionary mapping ticker symbols to sectors
    """
    try:
        # Use the repository to get company sectors
        sectors_dict = await CompanyRepository.get_company_sectors(ticker_symbols)

        if ticker_symbols:
            logger.info(f"Retrieved sector information for {len(sectors_dict)} companies with specified ticker symbols")
        else:
            logger.info(f"Retrieved sector information for {len(sectors_dict)} companies")
        return sectors_dict

    except Exception as e:
        logger.error(f"Error retrieving company sectors: {str(e)}")
        raise

async def get_all_sectors() -> List[str]:
    """
    Get all unique sectors from the database.

    Returns:
        List of sector names
    """
    try:
        # Use the repository to get all sectors
        sectors = await CompanyRepository.get_all_sectors()

        logger.info(f"Retrieved {len(sectors)} unique sectors from the database")
        return sectors

    except Exception as e:
        logger.error(f"Error retrieving sectors: {str(e)}")
        raise

async def get_industry_groups() -> List[str]:
    """
    Get all unique industry groups from the database.

    Returns:
        List of industry group names
    """
    try:
        # Use the repository to get industry groups
        industry_groups = await CompanyRepository.get_industry_groups()

        logger.info(f"Retrieved {len(industry_groups)} industry groups from the database")
        return industry_groups

    except Exception as e:
        logger.error(f"Error retrieving industry groups: {str(e)}")
        raise

async def get_companies_by_industry_groups(industry_groups: List[str]) -> Dict[str, Dict[str, str]]:
    """
    Get companies grouped by the specified industry groups.

    Args:
        industry_groups: List of industry group names to retrieve companies for

    Returns:
        Dictionary mapping industry groups to dictionaries of company names and ticker symbols
    """
    try:
        if not industry_groups:
            return {}

        # Use the repository to get companies by industry groups
        companies_by_industry = await CompanyRepository.get_companies_by_industry_groups(industry_groups)

        logger.info(f"Retrieved companies for {len(companies_by_industry)} industry groups")
        return companies_by_industry

    except Exception as e:
        logger.error(f"Error retrieving companies by industry groups: {str(e)}")
        raise

async def get_companies_by_tickers(tickers: List[str]) -> Dict[str, str]:
    """
    Get company information for specific ticker symbols.

    Args:
        tickers: List of ticker symbols to retrieve

    Returns:
        Dictionary mapping company names to ticker symbols.
        For companies with multiple share types (ORDINARY and NON_VOTING),
        the company name will be appended with the share type in parentheses.
    """
    try:
        if not tickers:
            return {}

        # Use the repository to get companies by tickers
        companies_dict = await CompanyRepository.get_companies_by_tickers(tickers)

        logger.info(f"Retrieved {len(companies_dict)} companies for the specified tickers")
        return companies_dict

    except Exception as e:
        logger.error(f"Error retrieving companies by tickers: {str(e)}")
        raise

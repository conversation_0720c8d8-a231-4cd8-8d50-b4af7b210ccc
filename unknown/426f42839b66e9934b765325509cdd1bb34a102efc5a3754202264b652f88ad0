"""
Company Models

This module defines the data models for company-related API requests and responses.
"""

from typing import Dict, List, Optional
from pydantic import BaseModel, Field


class Company(BaseModel):
    """Model representing a company as stored in the MongoDB database."""
    SECURITY_ID: int = Field(..., description="Security ID of the company")
    COMPANY_NAME: str = Field(..., description="Name of the company")
    TICKER_SYMBOL: str = Field(..., description="Ticker symbol of the company")
    SHARE_TYPE: str = Field(..., description="Type of share (e.g., ORDINARY, NON_VOTING)")
    GICS_SECTOR: str = Field(..., description="GICS sector the company belongs to")
    GICS_INDUSTRY_GROUP: str = Field(..., description="GICS industry group the company belongs to")
    S_PSL20: bool = Field(..., alias="S&PSL20", description="Whether the company is part of the S&PSL20 index")

    class Config:
        """Pydantic model configuration."""
        populate_by_name = True  # Allow populating by alias name


class CompaniesResponse(BaseModel):
    """Response model for the companies endpoint."""
    companies: Dict[str, str] = Field(
        ...,
        description="Dictionary mapping company names to ticker symbols"
    )


class SectorsResponse(BaseModel):
    """Response model for the sectors endpoint."""
    sectors: Dict[str, str] = Field(
        ...,
        description="Dictionary mapping ticker symbols to sectors"
    )


class AllSectorsResponse(BaseModel):
    """Response model for the all-sectors endpoint."""
    sectors: List[str] = Field(
        ...,
        description="List of all unique sectors"
    )


class IndustryGroupsResponse(BaseModel):
    """Response model for the industry-groups endpoint."""
    industry_groups: List[str] = Field(
        ...,
        description="List of all industry groups"
    )


class CompaniesByIndustryResponse(BaseModel):
    """Response model for the companies-by-industry endpoint."""
    companies: Dict[str, Dict[str, str]] = Field(
        ...,
        description="Dictionary mapping industry groups to dictionaries of company names and ticker symbols"
    )
    warning: Optional[str] = Field(
        None,
        description="Warning message if less than 5 companies are selected"
    )
# Examples

This directory contains example scripts that demonstrate how to use the CSE Portfolio Builder API.

## Error Handling Example

The `error_handling_example.py` script demonstrates how to handle errors from the API. It shows:

1. How to parse error responses
2. How to extract error codes and messages
3. How to handle different types of errors (validation errors, not found errors, etc.)
4. How to implement a robust API client that handles errors gracefully

### Running the Example

To run the error handling example:

```bash
# Make sure the API server is running
python -m examples.error_handling_example
```

### Example Output

```
Example 1: Invalid Portfolio ID Format
--------------------------------------------------
Error: Invalid portfolio ID format: invalid-id
Error Code: INVALID_PORTFOLIO_ID
Status Code: 400

Example 2: Non-existent Portfolio ID
--------------------------------------------------
Error: Portfolio with ID PF-999999-nonexistent not found
Error Code: PORTFOLIO_NOT_FOUND
Status Code: 404

Example 3: Empty Industry Groups List
--------------------------------------------------
Error: List should have at least 1 item after validation, not 0
Error Code: VALIDATION_ERROR
Status Code: 422
Validation Errors:
  - body.industry_groups: List should have at least 1 item after validation, not 0

Example 4: Non-existent Industry Group
--------------------------------------------------
Error: None of the requested industry groups were found: ['NON_EXISTENT_GROUP']
Error Code: INDUSTRY_GROUPS_NOT_FOUND
Status Code: 404

Example 5: Empty Ticker Symbols List
--------------------------------------------------
Error: At least one ticker symbol must be specified
Error Code: MISSING_TICKER_SYMBOLS
Status Code: 400

Example 6: Non-existent Ticker Symbol
--------------------------------------------------
Error: No sectors found for the specified companies
Error Code: SECTORS_NOT_FOUND
Status Code: 404

Example 7: Successful Request
--------------------------------------------------
Request successful!
Message: Successfully retrieved sectors for 2 companies
Sectors: {
  "LOLC.N0000": "FINANCIALS",
  "JKH.N0000": "INDUSTRIALS"
}
```

## Key Concepts

### Error Response Format

All error responses from the API follow this standard format:

```json
{
  "detail": {
    "detail": "Error message describing what went wrong",
    "error_code": "ERROR_CODE_FOR_PROGRAMMATIC_HANDLING",
    "status_code": 400,
    "errors": [
      {
        "loc": ["body", "field_name"],
        "msg": "Specific validation error message",
        "type": "error_type"
      }
    ]
  }
}
```

### Error Handling Best Practices

1. **Check HTTP Status Codes**: Always check the HTTP status code first to determine the general category of the error.
2. **Extract Error Codes**: Use the `error_code` field to determine the specific error for programmatic handling.
3. **Handle Validation Errors**: For 422 Unprocessable Entity responses, check the `errors` array for field-specific validation errors.
4. **Implement Retry Logic**: For certain errors (e.g., network errors, rate limiting), implement retry logic with exponential backoff.
5. **Log Full Error Responses**: Log the full error response for debugging purposes.

### Common Error Scenarios

- **Invalid Input**: 400 Bad Request with specific error codes like `INVALID_PORTFOLIO_ID` or `MISSING_TICKER_SYMBOLS`
- **Resource Not Found**: 404 Not Found with error codes like `PORTFOLIO_NOT_FOUND` or `SECTORS_NOT_FOUND`
- **Validation Errors**: 422 Unprocessable Entity with detailed validation errors in the `errors` array
- **Server Errors**: 500 Internal Server Error with error codes like `PORTFOLIO_UPDATE_ERROR` or `DATABASE_ERROR`

## Additional Examples

More examples will be added to demonstrate other aspects of the API, such as:

- Portfolio creation and management
- Portfolio analysis and optimization
- Working with companies and industry groups
- Handling batch operations

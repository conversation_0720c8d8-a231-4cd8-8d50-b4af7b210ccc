"""
Benchmark Service

This module provides functionality for benchmarking portfolios against market indices.
It calculates percentage changes for portfolios and indices from a common start date.
"""
from typing import Dict, Any, List, Tuple
import logging
from app.db.session import get_database
from app.utils.db_utils import safe_mongodb_operation
from app.utils.date_utils import validate_date_range, get_mongodb_date_query

# Configure logging
logger = logging.getLogger(__name__)

async def get_portfolio_value_history(
    portfolio_id: str,
    from_date: str,
    to_date: str
) -> List[Dict[str, Any]]:
    """
    Get historical portfolio values for a specific portfolio and date range.

    Args:
        portfolio_id: ID of the portfolio
        from_date: Start date in YYYY-MM-DD format
        to_date: End date in YYYY-MM-DD format

    Returns:
        List of portfolio value history documents sorted by date
    """
    try:
        # Get the database for the current event loop
        db = await get_database()

        # Validate and standardize date range
        validated_from_date, validated_to_date = validate_date_range(from_date, to_date)

        # Build query with date range
        date_query = get_mongodb_date_query(validated_from_date, validated_to_date)

        # Complete query with portfolio ID
        query = {
            "portfolioId": portfolio_id,
            **date_query
        }

        # Get portfolio value history sorted by date
        history = await safe_mongodb_operation(
            lambda: db["portfolio_value_history"].find(query).sort("date", 1).to_list(length=None)
        )

        if not history:
            logger.warning(f"No portfolio value history found for portfolio {portfolio_id} in the specified date range")
            return []

        # Convert Decimal128 values to float for easier processing
        for record in history:
            if "totalMarketValue" in record and hasattr(record["totalMarketValue"], "to_decimal"):
                record["totalMarketValue"] = float(record["totalMarketValue"].to_decimal())

        return history
    except Exception as e:
        logger.error(f"Error retrieving portfolio value history for {portfolio_id}: {str(e)}")
        return []

async def get_index_value_history(
    index_name: str,
    from_date: str,
    to_date: str
) -> List[Dict[str, Any]]:
    """
    Get historical index values for a specific index and date range.

    Args:
        index_name: Name of the index (ASPI or S&PSL20)
        from_date: Start date in YYYY-MM-DD format
        to_date: End date in YYYY-MM-DD format

    Returns:
        List of index value history documents sorted by date
    """
    try:
        # Get the database for the current event loop
        db = await get_database()

        # Validate and standardize date range
        validated_from_date, validated_to_date = validate_date_range(from_date, to_date)

        # Build query with date range
        date_query = get_mongodb_date_query(validated_from_date, validated_to_date)

        # Complete query with index name
        query = {
            "indexName": index_name,
            **date_query
        }

        # Get index value history sorted by date
        history = await safe_mongodb_operation(
            lambda: db["historical_index_data"].find(query).sort("date", 1).to_list(length=None)
        )

        if not history:
            logger.warning(f"No index value history found for {index_name} in the specified date range")
            return []

        # Convert Decimal128 values to float for easier processing
        for record in history:
            if "value" in record and hasattr(record["value"], "to_decimal"):
                record["value"] = float(record["value"].to_decimal())

        return history
    except Exception as e:
        logger.error(f"Error retrieving index value history for {index_name}: {str(e)}")
        return []

def calculate_percentage_change(
    values: List[Dict[str, Any]],
    value_key: str,
    date_key: str = "date"
) -> List[Dict[str, Any]]:
    """
    Calculate percentage change from the first value in the list.

    Args:
        values: List of dictionaries containing values
        value_key: Key for the value field in the dictionaries
        date_key: Key for the date field in the dictionaries

    Returns:
        List of dictionaries with date and percentage change
    """
    if not values:
        return []

    # Get the base value (first value in the list)
    base_value = values[0][value_key]

    # Handle zero or very small base values
    if base_value == 0:
        logger.warning(f"Base value for {value_key} is zero. Using absolute values instead of percentage changes.")
        # If base value is zero, we'll use absolute values instead of percentage changes
        result = []
        for item in values:
            result.append({
                "date": item[date_key],
                "value": item[value_key],
                "pct_change": 0.0 if item == values[0] else None  # First point is 0%, others are null
            })
        return result
    elif abs(base_value) < 0.000001:  # Very small value
        logger.warning(f"Base value for {value_key} is very small ({base_value}). This may lead to extreme percentage changes.")

    # Calculate percentage change for each value
    result = []
    for item in values:
        current_value = item[value_key]
        try:
            pct_change = ((current_value / base_value) - 1) * 100
            # Round to 4 decimal places to avoid floating point issues
            pct_change = round(pct_change, 4)
        except (ZeroDivisionError, TypeError, ValueError) as e:
            logger.error(f"Error calculating percentage change: {str(e)}")
            pct_change = None

        result.append({
            "date": item[date_key],
            "value": current_value,
            "pct_change": pct_change
        })

    return result

def align_time_series(
    portfolio_data: List[Dict[str, Any]],
    index_data: Dict[str, List[Dict[str, Any]]]
) -> Tuple[List[Dict[str, Any]], Dict[str, List[Dict[str, Any]]]]:
    """
    Align time series data to ensure all series have the same dates.

    This function finds the common date range and filters all series to that range.
    If no common dates are found across all series, it will use the portfolio dates
    and align available index data to those dates.

    Args:
        portfolio_data: Portfolio percentage change data
        index_data: Dictionary mapping index names to their percentage change data

    Returns:
        Tuple of aligned portfolio data and index data
    """
    if not portfolio_data:
        return [], {}

    if not index_data:
        # If no index data is available, just return the portfolio data
        portfolio_data.sort(key=lambda x: x["date"])
        return portfolio_data, {}

    # Get all portfolio dates using dateStr for comparison
    portfolio_date_strs = {item.get("dateStr", item["date"].date().isoformat()) for item in portfolio_data}

    # Try to find common dates across all series first using date strings
    common_date_strs = portfolio_date_strs.copy()
    for data in index_data.values():
        index_date_strs = {item.get("dateStr", item["date"].date().isoformat()) for item in data}
        common_date_strs &= index_date_strs

    # If no common dates across all series, use portfolio dates and available index data
    if not common_date_strs:
        logger.warning("No common dates found across all series. Using portfolio dates and available index data.")

        # Filter portfolio data (no change needed, using all portfolio dates)
        filtered_portfolio_data = sorted(portfolio_data, key=lambda x: x["date"])

        # For each index, find dates that overlap with portfolio dates
        filtered_index_data = {}
        for index_name, data in index_data.items():
            # Create a mapping of date strings to index data points
            index_date_map = {}
            for item in data:
                date_str = item.get("dateStr", item["date"].date().isoformat())
                index_date_map[date_str] = item

            # Find overlapping dates
            overlap_date_strs = portfolio_date_strs & set(index_date_map.keys())

            if overlap_date_strs:
                # Create a list of index data points for overlapping dates
                filtered_index_data[index_name] = [index_date_map[date_str] for date_str in overlap_date_strs]
                filtered_index_data[index_name].sort(key=lambda x: x["date"])
                logger.info(f"Found {len(overlap_date_strs)} overlapping dates for {index_name}")
            else:
                logger.warning(f"No overlapping dates found for {index_name}")

        return filtered_portfolio_data, filtered_index_data

    # If common dates exist across all series, use the standard approach with date strings
    # Create mappings of date strings to data points
    portfolio_date_map = {}
    for item in portfolio_data:
        date_str = item.get("dateStr", item["date"].date().isoformat())
        portfolio_date_map[date_str] = item

    index_date_maps = {}
    for index_name, data in index_data.items():
        index_date_maps[index_name] = {}
        for item in data:
            date_str = item.get("dateStr", item["date"].date().isoformat())
            index_date_maps[index_name][date_str] = item

    # Filter data using common date strings
    filtered_portfolio_data = [portfolio_date_map[date_str] for date_str in common_date_strs
                              if date_str in portfolio_date_map]

    filtered_index_data = {}
    for index_name, date_map in index_date_maps.items():
        filtered_index_data[index_name] = [date_map[date_str] for date_str in common_date_strs
                                          if date_str in date_map]

    # Sort all series by date
    filtered_portfolio_data.sort(key=lambda x: x["date"])
    for index_name in filtered_index_data:
        filtered_index_data[index_name].sort(key=lambda x: x["date"])

    return filtered_portfolio_data, filtered_index_data

async def get_benchmark_performance_data(
    portfolio_id: str,
    from_date: str,
    to_date: str,
    benchmark_indices: List[str]
) -> Dict[str, Any]:
    """
    Get benchmark performance data for a portfolio compared to market indices.

    Args:
        portfolio_id: ID of the portfolio
        from_date: Start date in YYYY-MM-DD format
        to_date: End date in YYYY-MM-DD format
        benchmark_indices: List of index names to benchmark against

    Returns:
        Dictionary containing benchmark performance data
    """
    try:
        # Get portfolio value history
        portfolio_history = await get_portfolio_value_history(portfolio_id, from_date, to_date)

        if not portfolio_history:
            return {
                "success": False,
                "message": f"No portfolio value history found for portfolio {portfolio_id} in the specified date range"
            }

        # Get index value history for each benchmark index
        index_history = {}
        missing_indices = []
        for index_name in benchmark_indices:
            index_data = await get_index_value_history(index_name, from_date, to_date)
            if index_data:
                index_history[index_name] = index_data
            else:
                missing_indices.append(index_name)

        # Log warning about missing indices
        if missing_indices:
            logger.warning(f"No data found for the following indices: {', '.join(missing_indices)}")

        # Continue with available indices even if some are missing
        available_indices = [idx for idx in benchmark_indices if idx not in missing_indices]

        # If no index data at all, return portfolio data only with a warning
        if not index_history:
            logger.warning("No index data available for any of the requested indices. Returning portfolio data only.")

            # Calculate percentage change for portfolio
            portfolio_pct_change = calculate_percentage_change(portfolio_history, "totalMarketValue")

            # Format the response with portfolio data only
            chart_data = []
            for point in portfolio_pct_change:
                # Convert date to string format for the response
                date_str = point["date"].date().isoformat()
                data_point = {
                    "date": date_str,
                    "portfolio_pct_change": point["pct_change"]
                }
                chart_data.append(data_point)

            # Get start date as string
            start_date_str = portfolio_pct_change[0]["date"].date().isoformat() if portfolio_pct_change else None

            return {
                "success": True,
                "portfolio_id": portfolio_id,
                "from_date": from_date,
                "to_date": to_date,
                "start_date": start_date_str,
                "benchmark_indices": [],  # No indices available
                "chart_data": chart_data,
                "warning": "No index data available for the requested indices"
            }

        # First, align the dates to find the common start date
        # Sort all data by date
        portfolio_history.sort(key=lambda x: x["date"])
        for index_name in index_history:
            index_history[index_name].sort(key=lambda x: x["date"])

        # Find the earliest common date between portfolio and all indices
        # Use dateStr instead of date objects for more reliable comparison
        portfolio_dates = set(record["dateStr"] for record in portfolio_history)
        index_dates = {}
        for index_name, data in index_history.items():
            index_dates[index_name] = set(record["dateStr"] for record in data)

        # Find common dates across all series
        common_dates = portfolio_dates.copy()
        for dates in index_dates.values():
            common_dates &= dates

        if not common_dates:
            logger.warning("No common dates found between portfolio and indices")
            # Fall back to calculating percentage changes separately
            portfolio_pct_change = calculate_percentage_change(portfolio_history, "totalMarketValue")
            index_pct_change = {}
            for index_name, data in index_history.items():
                index_pct_change[index_name] = calculate_percentage_change(data, "value")
        else:
            # Find the earliest common date
            earliest_common_date = min(common_dates)
            logger.info(f"Using {earliest_common_date} as the common base date for normalization")

            # Find the base values for portfolio and indices on the earliest common date
            portfolio_base_value = None
            for record in portfolio_history:
                if record["dateStr"] == earliest_common_date:
                    portfolio_base_value = record["totalMarketValue"]
                    logger.info(f"Found portfolio base value {portfolio_base_value} for date {earliest_common_date}")
                    break

            index_base_values = {}
            for index_name, data in index_history.items():
                for record in data:
                    if record["dateStr"] == earliest_common_date:
                        index_base_values[index_name] = record["value"]
                        logger.info(f"Found {index_name} base value {record['value']} for date {earliest_common_date}")
                        break

            # Calculate percentage changes from the common base date
            portfolio_pct_change = []
            for record in portfolio_history:
                date_str = record["dateStr"]
                if date_str >= earliest_common_date:  # Only include dates on or after the common base date
                    pct_change = 0.0
                    if portfolio_base_value is not None and portfolio_base_value > 0:
                        pct_change = ((record["totalMarketValue"] / portfolio_base_value) - 1) * 100
                        pct_change = round(pct_change, 4)

                    portfolio_pct_change.append({
                        "date": record["date"],
                        "dateStr": record["dateStr"],
                        "value": record["totalMarketValue"],
                        "pct_change": pct_change
                    })

            index_pct_change = {}
            for index_name, data in index_history.items():
                index_pct_change[index_name] = []
                base_value = index_base_values.get(index_name)

                if base_value:
                    for record in data:
                        date_str = record["dateStr"]
                        if date_str >= earliest_common_date:  # Only include dates on or after the common base date
                            pct_change = 0.0
                            if base_value is not None and base_value > 0:
                                pct_change = ((record["value"] / base_value) - 1) * 100
                                pct_change = round(pct_change, 4)

                            index_pct_change[index_name].append({
                                "date": record["date"],
                                "dateStr": record["dateStr"],
                                "value": record["value"],
                                "pct_change": pct_change
                            })

        # Align time series to ensure all have the same dates
        aligned_portfolio, aligned_indices = align_time_series(portfolio_pct_change, index_pct_change)

        if not aligned_portfolio:
            return {
                "success": False,
                "message": "No portfolio data available after alignment"
            }

        # Format the response
        chart_data = []

        # Get the start date (earliest common date)
        start_date_str = None

        # Check if we have common dates from earlier calculation
        if common_dates:
            # Use the earliest common date
            start_date_str = min(common_dates)
        elif aligned_portfolio:
            # Fallback to the first date in the aligned portfolio data
            start_date = aligned_portfolio[0]["date"]
            start_date_str = start_date.date().isoformat() if start_date else None

        # Create a mapping of date strings to indices for each index
        index_date_maps = {}
        for index_name, points in aligned_indices.items():
            index_date_maps[index_name] = {point.get("dateStr", point["date"].date().isoformat()): point for point in points}

        # Build chart data points
        for portfolio_point in aligned_portfolio:
            date = portfolio_point["date"]
            # Use dateStr if available, otherwise convert date to string format
            date_str = portfolio_point.get("dateStr", date.date().isoformat())

            data_point = {
                "date": date_str,
                "portfolio_pct_change": portfolio_point["pct_change"]
            }

            # Add available index data for this date
            for index_name in aligned_indices:
                if date_str in index_date_maps.get(index_name, {}):
                    # Use the correct field name based on the index name
                    if index_name == "S&PSL20":
                        data_point["spsl20_pct_change"] = index_date_maps[index_name][date_str]["pct_change"]
                    else:
                        data_point[f"{index_name.lower()}_pct_change"] = index_date_maps[index_name][date_str]["pct_change"]
                else:
                    # Use the correct field name based on the index name
                    if index_name == "S&PSL20":
                        data_point["spsl20_pct_change"] = None
                    else:
                        data_point[f"{index_name.lower()}_pct_change"] = None

            chart_data.append(data_point)

        # Add warning message if some indices were missing
        warning_message = None
        if missing_indices:
            warning_message = f"No data found for the following indices: {', '.join(missing_indices)}"

        return {
            "success": True,
            "portfolio_id": portfolio_id,
            "from_date": from_date,
            "to_date": to_date,
            "start_date": start_date_str,  # Add the actual start date used for normalization as a string
            "benchmark_indices": available_indices,  # Only include indices with data
            "chart_data": chart_data,
            "warning": warning_message
        }
    except Exception as e:
        logger.error(f"Error generating benchmark performance data: {str(e)}")
        return {
            "success": False,
            "message": f"Error generating benchmark performance data: {str(e)}"
        }
